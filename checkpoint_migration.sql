-- Checkpoint System Migration
-- This migration adds checkpoint functionality while maintaining backward compatibility

-- Create or alter checkpoints table
DO $$
BEGIN
    -- Create table if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'checkpoints') THEN
        CREATE TABLE checkpoints (
            id SERIAL PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            valid_from TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
            color VARCHAR(7) DEFAULT '#2196F3'
        );
    END IF;

    -- Add columns if they don't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'checkpoints' AND column_name = 'description') THEN
        ALTER TABLE checkpoints ADD COLUMN description TEXT;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'checkpoints' AND column_name = 'created_at') THEN
        ALTER TABLE checkpoints ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'checkpoints' AND column_name = 'valid_from') THEN
        ALTER TABLE checkpoints ADD COLUMN valid_from TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'checkpoints' AND column_name = 'color') THEN
        ALTER TABLE checkpoints ADD COLUMN color VARCHAR(7) DEFAULT '#2196F3';
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'checkpoints' AND column_name = 'app_id') THEN
        ALTER TABLE checkpoints ADD COLUMN app_id INTEGER;

        -- Delete any existing checkpoints since they can't have a valid app_id
        -- This is safer than trying to assign them to a random app
        DELETE FROM checkpoints WHERE app_id IS NULL;

        -- Now make it NOT NULL and add constraints
        ALTER TABLE checkpoints ALTER COLUMN app_id SET NOT NULL;
        -- Add foreign key constraint
        ALTER TABLE checkpoints ADD CONSTRAINT fk_checkpoints_app_id
            FOREIGN KEY (app_id) REFERENCES apps(id) ON DELETE CASCADE;
    END IF;

    -- Add unique constraint if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'checkpoints_name_app_unique'
        AND table_name = 'checkpoints'
    ) THEN
        -- Drop old constraint if it exists
        IF EXISTS (
            SELECT 1 FROM information_schema.table_constraints
            WHERE constraint_name = 'checkpoints_name_key'
            AND table_name = 'checkpoints'
        ) THEN
            ALTER TABLE checkpoints DROP CONSTRAINT checkpoints_name_key;
        END IF;
        ALTER TABLE checkpoints ADD CONSTRAINT checkpoints_name_app_unique UNIQUE (name, app_id);
    END IF;
END $$;

-- Create or alter active_checkpoints table
DO $$
BEGIN
    -- Create table if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'active_checkpoints') THEN
        CREATE TABLE active_checkpoints (
            id SERIAL PRIMARY KEY,
            checkpoint_id INTEGER NOT NULL,
            activated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
    END IF;

    -- Add columns if they don't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'active_checkpoints' AND column_name = 'checkpoint_id') THEN
        ALTER TABLE active_checkpoints ADD COLUMN checkpoint_id INTEGER NOT NULL;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'active_checkpoints' AND column_name = 'activated_at') THEN
        ALTER TABLE active_checkpoints ADD COLUMN activated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'active_checkpoints' AND column_name = 'app_id') THEN
        -- Delete any existing active_checkpoints since they can't have a valid app_id
        DELETE FROM active_checkpoints;

        ALTER TABLE active_checkpoints ADD COLUMN app_id INTEGER;

        -- Delete any records that still have NULL app_id after column addition
        DELETE FROM active_checkpoints WHERE app_id IS NULL;

        -- Now make it NOT NULL and add constraints
        ALTER TABLE active_checkpoints ALTER COLUMN app_id SET NOT NULL;
        -- Add foreign key constraint
        ALTER TABLE active_checkpoints ADD CONSTRAINT fk_active_checkpoints_app_id
            FOREIGN KEY (app_id) REFERENCES apps(id) ON DELETE CASCADE;
    END IF;

    -- Add foreign key constraint for checkpoint_id if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'fk_active_checkpoints_checkpoint_id'
        AND table_name = 'active_checkpoints'
    ) THEN
        -- Clean up any orphaned records before adding constraint
        DELETE FROM active_checkpoints
        WHERE checkpoint_id NOT IN (SELECT id FROM checkpoints);

        ALTER TABLE active_checkpoints ADD CONSTRAINT fk_active_checkpoints_checkpoint_id
            FOREIGN KEY (checkpoint_id) REFERENCES checkpoints(id) ON DELETE CASCADE;
    END IF;

    -- Add unique constraint if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'active_checkpoints_checkpoint_app_unique'
        AND table_name = 'active_checkpoints'
    ) THEN
        -- Drop old constraint if it exists
        IF EXISTS (
            SELECT 1 FROM information_schema.table_constraints
            WHERE constraint_name = 'active_checkpoints_checkpoint_id_key'
            AND table_name = 'active_checkpoints'
        ) THEN
            ALTER TABLE active_checkpoints DROP CONSTRAINT active_checkpoints_checkpoint_id_key;
        END IF;
        ALTER TABLE active_checkpoints ADD CONSTRAINT active_checkpoints_checkpoint_app_unique UNIQUE (checkpoint_id, app_id);
    END IF;
END $$;

-- Add checkpoint_id to timeline table (nullable for backward compatibility)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'timeline' AND column_name = 'checkpoint_id') THEN
        ALTER TABLE timeline ADD COLUMN checkpoint_id INTEGER;
    END IF;
END $$;

-- Add foreign key constraint for timeline.checkpoint_id
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'fk_timeline_checkpoint'
        AND table_name = 'timeline'
    ) THEN
        -- Clean up any orphaned timeline records before adding constraint
        UPDATE timeline SET checkpoint_id = NULL
        WHERE checkpoint_id IS NOT NULL
        AND checkpoint_id NOT IN (SELECT id FROM checkpoints);

        ALTER TABLE timeline ADD CONSTRAINT fk_timeline_checkpoint
            FOREIGN KEY (checkpoint_id) REFERENCES checkpoints(id) ON DELETE SET NULL;
    END IF;
END $$;

-- Create or alter checkpoint_durations table
DO $$
BEGIN
    -- Create table if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'checkpoint_durations') THEN
        CREATE TABLE checkpoint_durations (
            id SERIAL PRIMARY KEY,
            checkpoint_id INTEGER NOT NULL,
            app_id INTEGER NOT NULL,
            duration INTEGER DEFAULT 0,
            sessions_count INTEGER DEFAULT 0,
            last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );

        -- Add foreign key constraints
        ALTER TABLE checkpoint_durations ADD CONSTRAINT fk_checkpoint_durations_checkpoint_id
            FOREIGN KEY (checkpoint_id) REFERENCES checkpoints(id) ON DELETE CASCADE;
        ALTER TABLE checkpoint_durations ADD CONSTRAINT fk_checkpoint_durations_app_id
            FOREIGN KEY (app_id) REFERENCES apps(id) ON DELETE CASCADE;

        -- Add unique constraint
        ALTER TABLE checkpoint_durations ADD CONSTRAINT checkpoint_durations_checkpoint_app_unique UNIQUE (checkpoint_id, app_id);
    ELSE
        -- Add columns if they don't exist
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'checkpoint_durations' AND column_name = 'duration') THEN
            ALTER TABLE checkpoint_durations ADD COLUMN duration INTEGER DEFAULT 0;
        END IF;

        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'checkpoint_durations' AND column_name = 'sessions_count') THEN
            ALTER TABLE checkpoint_durations ADD COLUMN sessions_count INTEGER DEFAULT 0;
        END IF;

        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'checkpoint_durations' AND column_name = 'last_updated') THEN
            ALTER TABLE checkpoint_durations ADD COLUMN last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
        END IF;

        -- Add foreign key constraints if they don't exist
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.table_constraints
            WHERE constraint_name = 'fk_checkpoint_durations_checkpoint_id'
            AND table_name = 'checkpoint_durations'
        ) THEN
            -- Clean up orphaned records
            DELETE FROM checkpoint_durations
            WHERE checkpoint_id NOT IN (SELECT id FROM checkpoints);

            ALTER TABLE checkpoint_durations ADD CONSTRAINT fk_checkpoint_durations_checkpoint_id
                FOREIGN KEY (checkpoint_id) REFERENCES checkpoints(id) ON DELETE CASCADE;
        END IF;

        IF NOT EXISTS (
            SELECT 1 FROM information_schema.table_constraints
            WHERE constraint_name = 'fk_checkpoint_durations_app_id'
            AND table_name = 'checkpoint_durations'
        ) THEN
            -- Clean up orphaned records
            DELETE FROM checkpoint_durations
            WHERE app_id NOT IN (SELECT id FROM apps);

            ALTER TABLE checkpoint_durations ADD CONSTRAINT fk_checkpoint_durations_app_id
                FOREIGN KEY (app_id) REFERENCES apps(id) ON DELETE CASCADE;
        END IF;

        -- Add unique constraint if it doesn't exist
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.table_constraints
            WHERE constraint_name = 'checkpoint_durations_checkpoint_app_unique'
            AND table_name = 'checkpoint_durations'
        ) THEN
            ALTER TABLE checkpoint_durations ADD CONSTRAINT checkpoint_durations_checkpoint_app_unique UNIQUE (checkpoint_id, app_id);
        END IF;
    END IF;
END $$;

-- Create or alter timeline_checkpoints junction table
DO $$
BEGIN
    -- Create table if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'timeline_checkpoints') THEN
        CREATE TABLE timeline_checkpoints (
            id SERIAL PRIMARY KEY,
            timeline_id INTEGER NOT NULL,
            checkpoint_id INTEGER NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );

        -- Add foreign key constraints
        ALTER TABLE timeline_checkpoints ADD CONSTRAINT fk_timeline_checkpoints_timeline_id
            FOREIGN KEY (timeline_id) REFERENCES timeline(id) ON DELETE CASCADE;
        ALTER TABLE timeline_checkpoints ADD CONSTRAINT fk_timeline_checkpoints_checkpoint_id
            FOREIGN KEY (checkpoint_id) REFERENCES checkpoints(id) ON DELETE CASCADE;

        -- Add unique constraint
        ALTER TABLE timeline_checkpoints ADD CONSTRAINT timeline_checkpoints_timeline_checkpoint_unique UNIQUE (timeline_id, checkpoint_id);
    ELSE
        -- Add columns if they don't exist
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'timeline_checkpoints' AND column_name = 'created_at') THEN
            ALTER TABLE timeline_checkpoints ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
        END IF;

        -- Add foreign key constraints if they don't exist
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.table_constraints
            WHERE constraint_name = 'fk_timeline_checkpoints_timeline_id'
            AND table_name = 'timeline_checkpoints'
        ) THEN
            -- Clean up orphaned records
            DELETE FROM timeline_checkpoints
            WHERE timeline_id NOT IN (SELECT id FROM timeline);

            ALTER TABLE timeline_checkpoints ADD CONSTRAINT fk_timeline_checkpoints_timeline_id
                FOREIGN KEY (timeline_id) REFERENCES timeline(id) ON DELETE CASCADE;
        END IF;

        IF NOT EXISTS (
            SELECT 1 FROM information_schema.table_constraints
            WHERE constraint_name = 'fk_timeline_checkpoints_checkpoint_id'
            AND table_name = 'timeline_checkpoints'
        ) THEN
            -- Clean up orphaned records
            DELETE FROM timeline_checkpoints
            WHERE checkpoint_id NOT IN (SELECT id FROM checkpoints);

            ALTER TABLE timeline_checkpoints ADD CONSTRAINT fk_timeline_checkpoints_checkpoint_id
                FOREIGN KEY (checkpoint_id) REFERENCES checkpoints(id) ON DELETE CASCADE;
        END IF;

        -- Add unique constraint if it doesn't exist
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.table_constraints
            WHERE constraint_name = 'timeline_checkpoints_timeline_checkpoint_unique'
            AND table_name = 'timeline_checkpoints'
        ) THEN
            ALTER TABLE timeline_checkpoints ADD CONSTRAINT timeline_checkpoints_timeline_checkpoint_unique UNIQUE (timeline_id, checkpoint_id);
        END IF;
    END IF;
END $$;

-- Add unique constraint to timeline table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'timeline_date_app_unique'
        AND table_name = 'timeline'
    ) THEN
        ALTER TABLE timeline ADD CONSTRAINT timeline_date_app_unique UNIQUE (date, app_id);
    END IF;
END $$;

-- Updated upsert_timeline procedure to handle multiple active checkpoints per app
CREATE OR REPLACE FUNCTION upsert_timeline_multiple_checkpoints(
    p_app_name VARCHAR(255),
    p_date DATE
) RETURNS INTEGER AS $$
DECLARE
    v_app_id INTEGER;
    v_timeline_id INTEGER;
    v_checkpoint_id INTEGER;
    v_next_id INTEGER;
BEGIN
    -- Get app_id
    SELECT id INTO v_app_id FROM apps WHERE name = p_app_name;

    IF v_app_id IS NULL THEN
        RAISE EXCEPTION 'App not found: %', p_app_name;
    END IF;

    -- Check if timeline entry already exists
    SELECT id INTO v_timeline_id FROM timeline WHERE date = p_date AND app_id = v_app_id;

    IF v_timeline_id IS NOT NULL THEN
        -- Update existing timeline entry
        UPDATE timeline SET duration = duration + 1 WHERE id = v_timeline_id;
    ELSE
        -- Get next ID for new timeline entry
        SELECT COALESCE(MAX(id), 0) + 1 INTO v_next_id FROM timeline;

        -- Insert new timeline entry with manual ID
        INSERT INTO timeline (id, date, duration, app_id)
        VALUES (v_next_id, p_date, 1, v_app_id);

        v_timeline_id := v_next_id;
    END IF;

    -- Associate this timeline entry with all currently active checkpoints for this specific app
    FOR v_checkpoint_id IN
        SELECT ac.checkpoint_id FROM active_checkpoints ac
        JOIN checkpoints c ON c.id = ac.checkpoint_id
        WHERE ac.app_id = v_app_id AND c.valid_from <= p_date
    LOOP
        -- Insert into timeline_checkpoints junction table
        INSERT INTO timeline_checkpoints (timeline_id, checkpoint_id)
        VALUES (v_timeline_id, v_checkpoint_id)
        ON CONFLICT (timeline_id, checkpoint_id) DO NOTHING;

        -- Update checkpoint_durations (only duration, sessions_count handled separately)
        INSERT INTO checkpoint_durations (checkpoint_id, app_id, duration, sessions_count, last_updated)
        VALUES (v_checkpoint_id, v_app_id, 1, 0, CURRENT_TIMESTAMP)
        ON CONFLICT (checkpoint_id, app_id)
        DO UPDATE SET
            duration = checkpoint_durations.duration + 1,
            last_updated = CURRENT_TIMESTAMP;
    END LOOP;

    RETURN v_timeline_id;
END;
$$ LANGUAGE plpgsql;

-- Backward compatibility function (still used by current Dart code)
CREATE OR REPLACE FUNCTION upsert_timeline_checkpoint(
    p_app_name VARCHAR(255),
    p_date DATE,
    p_checkpoint_id INTEGER DEFAULT NULL
) RETURNS VOID AS $$
DECLARE
    v_app_id INTEGER;
BEGIN
    -- Get app_id
    SELECT id INTO v_app_id FROM apps WHERE name = p_app_name;

    IF v_app_id IS NULL THEN
        RAISE EXCEPTION 'App not found: %', p_app_name;
    END IF;

    -- If checkpoint_id is provided, use the old single-checkpoint logic
    IF p_checkpoint_id IS NOT NULL THEN
        -- Ensure the checkpoint is active for this specific app
        INSERT INTO active_checkpoints (checkpoint_id, app_id)
        VALUES (p_checkpoint_id, v_app_id)
        ON CONFLICT (checkpoint_id, app_id) DO NOTHING;
    END IF;

    -- Use the new multiple checkpoints function
    PERFORM upsert_timeline_multiple_checkpoints(p_app_name, p_date);
END;
$$ LANGUAGE plpgsql;

-- Helper functions for managing active checkpoints per app
CREATE OR REPLACE FUNCTION activate_checkpoint(p_checkpoint_id INTEGER, p_app_id INTEGER) RETURNS VOID AS $$
BEGIN
    INSERT INTO active_checkpoints (checkpoint_id, app_id)
    VALUES (p_checkpoint_id, p_app_id)
    ON CONFLICT (checkpoint_id, app_id) DO NOTHING;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION deactivate_checkpoint(p_checkpoint_id INTEGER, p_app_id INTEGER) RETURNS VOID AS $$
BEGIN
    DELETE FROM active_checkpoints WHERE checkpoint_id = p_checkpoint_id AND app_id = p_app_id;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION clear_all_active_checkpoints_for_app(p_app_id INTEGER) RETURNS VOID AS $$
BEGIN
    DELETE FROM active_checkpoints WHERE app_id = p_app_id;
END;
$$ LANGUAGE plpgsql;

-- Legacy function for backward compatibility (now clears all apps)
CREATE OR REPLACE FUNCTION clear_all_active_checkpoints() RETURNS VOID AS $$
BEGIN
    DELETE FROM active_checkpoints;
END;
$$ LANGUAGE plpgsql;

-- Create indexes for better performance
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_timeline_checkpoint_id') THEN
        CREATE INDEX idx_timeline_checkpoint_id ON timeline(checkpoint_id);
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_checkpoint_durations_checkpoint_app') THEN
        CREATE INDEX idx_checkpoint_durations_checkpoint_app ON checkpoint_durations(checkpoint_id, app_id);
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_active_checkpoints_checkpoint_app') THEN
        CREATE INDEX idx_active_checkpoints_checkpoint_app ON active_checkpoints(checkpoint_id, app_id);
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_checkpoints_app_id') THEN
        CREATE INDEX idx_checkpoints_app_id ON checkpoints(app_id);
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_timeline_checkpoints_timeline_id') THEN
        CREATE INDEX idx_timeline_checkpoints_timeline_id ON timeline_checkpoints(timeline_id);
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_timeline_checkpoints_checkpoint_id') THEN
        CREATE INDEX idx_timeline_checkpoints_checkpoint_id ON timeline_checkpoints(checkpoint_id);
    END IF;
END $$;

-- Data migration: Handle existing data and schema changes
DO $$
DECLARE
    v_app_id INTEGER;
    v_checkpoint_id INTEGER;
BEGIN
    -- Clean up any existing global checkpoints that don't have app_id
    -- This handles the case where checkpoints table existed but without app_id
    IF EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'checkpoints' AND column_name = 'app_id'
    ) THEN
        -- Delete any checkpoints that somehow have NULL app_id
        DELETE FROM checkpoints WHERE app_id IS NULL;
    END IF;

    -- Clean up any existing active_checkpoints that don't have app_id
    IF EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'active_checkpoints' AND column_name = 'app_id'
    ) THEN
        -- Delete any active_checkpoints that somehow have NULL app_id
        DELETE FROM active_checkpoints WHERE app_id IS NULL;
    END IF;
END $$;
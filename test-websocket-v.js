const WebSocket = require('ws');

// Test the V time tracker WebSocket server
const ws = new WebSocket('ws://localhost:6754/ws');

ws.on('open', function open() {
    console.log('Connected to V time tracker WebSocket server');

    // Test ping
    console.log('Sending ping...');
    ws.send('ping');

        // Test legacy pause command
    setTimeout(() => {
        console.log('Sending get_app_by_name command...');
        ws.send(JSON.stringify({
            type: 'get_app_by_name',
            payload: '{"name": "BF2042"}'
        }));
    }, 1000);

    // Test get_apps command
    setTimeout(() => {
        console.log('Sending get_apps command...');
        ws.send(JSON.stringify({
            type: 'get_apps',
            payload: '{}'
        }));
    }, 1000);

    // Test get_tracking_status command
    setTimeout(() => {
        console.log('Sending get_tracking_status command...');
        ws.send(JSON.stringify({
            type: 'get_tracking_status',
            payload: '{}'
        }));
    }, 1000);

    // Test legacy pause command
    setTimeout(() => {
        console.log('Sending legacy pause command...');
        ws.send('pause');
    }, 1000);
});

ws.on('message', function message(data) {
    console.log('Received:', data.toString());
    try {
        const parsed = JSON.parse(data.toString());
        console.log('Parsed message:', JSON.stringify(parsed, null, 2));
    } catch (e) {
        console.log('Raw message (not JSON):', data.toString());
    }
});

ws.on('error', function error(err) {
    console.error('WebSocket error:', err);
});

ws.on('close', function close() {
    console.log('WebSocket connection closed');
});

// Close after 10 seconds
setTimeout(() => {
    console.log('Closing connection...');
    ws.close();
}, 10000);
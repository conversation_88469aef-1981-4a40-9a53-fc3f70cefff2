#!/usr/bin/env -S v run

import os
import flag
import db.pg
import time
import net.websocket as socket
import net
import json

@[table: 'apps']
struct Apps {
	id int @[primary; unique]
	duration ?int
	launches ?int
	name ?string
	longest_session ?int
	product_name ?string
	longest_session_on ?string
}

@[table: 'timeline']
struct Timeline {
	id int @[primary; unique]
	date ?string
	duration ?int
	app_id int @[fkey: 'timeline_app_id_fkey']
}

struct WebSocketMessage {
	type_ string @[json: 'type']
	payload string
}

struct WebSocketCommand {
	type_ string @[json: 'type']
	payload string
}

struct ApiResponse[T] {
	success bool
	data ?T
	message ?string
}

struct TrackingStatus {
mut:
	is_tracking bool
	is_paused bool
	current_app ?string
	current_session_duration int
	session_start_time ?string
}

struct AppStatistics {
	app Apps
	total_duration int
	today_duration int
	week_duration int
	month_duration int
	average_session_length f64
	recent_sessions []Timeline
}

struct SessionCount {
	app_id int
	session_count i64
}

struct InsertAppRequest {
	name string
}

struct CommandRequest {
	command string
}

// WebSocket event constants
const tracking_status_update = 'tracking_status_update'
const app_update = 'app_update'
const session_update = 'session_update'
const apps_list = 'apps_list'
const timeline_data = 'timeline_data'
const statistics_data = 'statistics_data'
const session_counts = 'session_counts'
const error_event = 'error'
const pong_event = 'pong'

enum Operation {
  launches
  apps
  status
  dbop
  longest
  insert
  broadcast
}

struct ChannelData {
  process string
  mut:
    status ?bool
    sql_string string
    sql_error string
    duration ?int
    launches ?int
    operations Operation
    ws_message ?WebSocketMessage
    tracking_status ?TrackingStatus
}

struct ProcMap {
  mut:
    pause bool
    processes map[string]bool
    tracking_status TrackingStatus
    connected_clients []socket.Client
}

fn is_process_running(process string, debug bool) bool {
	return execute('pgrep -f $process').exit_code == 0
}

fn get_all_apps(db pg.DB) ![]Apps {
	return sql db { select from Apps order by id }!
}

fn get_app_by_name(db pg.DB, name string) ?Apps {
	apps := sql db { select from Apps where name == name } or { return none }
	return if apps.len > 0 { apps[0] } else { none }
}

fn get_app_by_id(db pg.DB, id int) ?Apps {
	apps := sql db { select from Apps where id == id } or { return none }
	return if apps.len > 0 { apps[0] } else { none }
}

fn insert_new_app(db pg.DB, name string) ! {
	all_apps := get_all_apps(db)!
	new_id := if all_apps.len > 0 { all_apps.last().id + 1 } else { 1 }
	db.exec("INSERT INTO Apps (id, name) VALUES ($new_id, '$name')")!
}

fn delete_app(db pg.DB, id int) ! {
	db.exec("DELETE FROM Apps WHERE id = $id")!
}

fn get_all_timeline(db pg.DB) ![]Timeline {
	return sql db { select from Timeline order by date desc }!
}

fn get_timeline_by_app(db pg.DB, app_id int) ![]Timeline {
	return sql db { select from Timeline where app_id == app_id order by date desc }!
}

fn get_timeline_by_date_range(db pg.DB, start_date string, end_date string) ![]Timeline {
	// Simplified implementation using direct SQL
	return sql db { select from Timeline order by date desc }!
}

fn get_session_counts_by_app(db pg.DB) ![]SessionCount {
	// Simplified implementation - V's pg module might not support complex queries
	mut counts := []SessionCount{}
	apps := sql db { select from Apps }!
	for app in apps {
		timeline := sql db { select from Timeline where app_id == app.id }!
		counts << SessionCount{
			app_id: app.id
			session_count: i64(timeline.len)
		}
	}
	return counts
}

fn create_ws_message(type_ string, payload string) WebSocketMessage {
	return WebSocketMessage{
		type_: type_
		payload: payload
	}
}

fn create_error_message(error_msg string) WebSocketMessage {
	return create_ws_message(error_event, '{"message": "$error_msg"}')
}

fn create_pong_message() WebSocketMessage {
	return create_ws_message(pong_event, '{"message": "pong"}')
}

fn broadcast_to_clients(mut proc_map ProcMap, message WebSocketMessage) {
	message_json := json.encode(message)
	for mut client in proc_map.connected_clients {
		client.write_string(message_json) or { continue }
	}
}

fn handle_websocket_command(mut proc_map ProcMap, db pg.DB, command WebSocketCommand, debug bool) !WebSocketMessage {
	if debug { println('handle_websocket_command: $command.type_') }

	match command.type_ {
		'get_apps' {
			apps := get_all_apps(db)!
			apps_json := json.encode(apps)
			return create_ws_message(apps_list, '{"apps": $apps_json}')
		}
		'get_app_by_name' {
      name := json.decode(struct { name string }, command.payload) or { return create_error_message('Invalid JSON format: ${err}') }

      if app := get_app_by_name(db, name.name) {
        app_json := json.encode(app)
        return create_ws_message("app", '{"app": $app_json}')
      }

      return create_error_message("App not found")
		}
		'insert_app' {
			// TODO: Implement insert_app
			return create_error_message("Command not implemented in simplified version")
		}
		'delete_app' {
			// Simplified - would need proper JSON parsing for payload
			return create_error_message("Command not implemented in simplified version")
		}
		'get_tracking_status' {
			status_json := json.encode(proc_map.tracking_status)
			return create_ws_message(tracking_status_update, '{"status": $status_json}')
		}
		'tracking_command' {
			// Simplified - would need proper JSON parsing for payload
			return create_error_message("Command not implemented in simplified version")
		}
		'get_timeline' {
			timeline := get_all_timeline(db)!
			timeline_json := json.encode(timeline)
			return create_ws_message(timeline_data, '{"timeline": $timeline_json}')
		}
		'get_session_counts' {
			counts := get_session_counts_by_app(db)!
			counts_json := json.encode(counts)
			return create_ws_message(session_counts, '{"counts": $counts_json}')
		}
		'get_statistics' {
			apps := get_all_apps(db)!
			mut statistics := []AppStatistics{}

			for app in apps {
				timeline := get_timeline_by_app(db, app.id)!
				today := time.now().str().split(' ')[0]

				mut today_duration := 0
				mut week_duration := 0
				mut month_duration := 0

				for tl in timeline {
					if tl_date := tl.date {
						if tl_duration := tl.duration {
							if tl_date == today {
								today_duration += tl_duration
							}
							// Simplified week/month calculation
							week_duration += tl_duration
							month_duration += tl_duration
						}
					}
				}

				avg_session := if timeline.len > 0 {
					mut total := 0
					for tl in timeline {
						total += tl.duration or { 0 }
					}
					f64(total) / f64(timeline.len)
				} else { 0.0 }

				recent_sessions := if timeline.len > 10 { timeline[..10] } else { timeline }

				statistics << AppStatistics{
					app: app
					total_duration: app.duration or { 0 }
					today_duration: today_duration
					week_duration: week_duration
					month_duration: month_duration
					average_session_length: avg_session
					recent_sessions: recent_sessions
				}
			}

			statistics_json := json.encode(statistics)
			return create_ws_message(statistics_data, '{"statistics": $statistics_json}')
		}
		else {
			return create_error_message('Unknown command type: ${command.type_}')
		}
	}
}

fn (shared procMap ProcMap) enhanced_socket_service(db pg.DB, debug bool, ch chan ChannelData) ! {
	opt := socket.ServerOpt {}
	addr := net.AddrFamily.ip
	mut server := socket.new_server(addr, 6754, "/ws", opt)

	server.on_connect(fn[mut procMap, debug] (mut client socket.ServerClient) !bool {
		if debug { println('WebSocket client connected') }
		lock procMap {
			procMap.connected_clients << client.client
		}
		return true
	}) !

	server.on_close(fn[mut procMap, debug] (mut client socket.Client, code int, reason string) ! {
		if debug { println('WebSocket client disconnected: $code - $reason') }

		lock procMap {
			mut new_clients := []socket.Client{}
			for i := 0; i < procMap.connected_clients.len; i++ {
				if procMap.connected_clients[i] != client {
					new_clients << procMap.connected_clients[i]
				}
			}

			procMap.connected_clients = new_clients
		}
	})

	server.on_message(fn[mut procMap, db, debug, ch] (mut client socket.Client, msg &socket.Message) ! {
		payload := msg.payload.bytestr()

		if debug { println('Received WebSocket message: $payload') }

		// Handle raw ping messages
		if payload == 'ping' {
			if debug { println('Received ping, sending pong') }
			client.write_string('pong') or { return }
			return
		}

		// Handle empty messages
		if payload.trim_space() == '' {
			if debug { println('Received empty message, ignoring') }
			return
		}

		// Handle JSON WebSocket commands
		command := json.decode(WebSocketCommand, payload) or {
			if debug { println('Failed to decode JSON command: ${err}') }
			error_msg := create_error_message('Invalid JSON format: ${err}')
			client.write_string(json.encode(error_msg)) or { return }
			return
		}

		response := lock procMap {
			handle_websocket_command(mut procMap, db, command, debug) or {
				create_error_message('Command error: ${err}')
			}
		}

		client.write_string(json.encode(response)) or { return }
	})

	server.listen() !
}

fn (shared procMap ProcMap) enhanced_db_service(db pg.DB, debug bool, ch chan ChannelData) {
	for {
		channel_data := <-ch

		process := channel_data.process
		sql_string := channel_data.sql_string
		sql_err := channel_data.sql_error
		duration := channel_data.duration
		launches := channel_data.launches

		db_err := 'Could not update database.'

		match channel_data.operations {
			.launches {
				if debug { println("launch op") }
				sql db { update Apps set launches = launches where name == process } or { eprintln(db_err) }
			}
			.apps {
				if debug { println("app op") }
				sql db { update Apps set duration = duration where name == process } or { eprintln(db_err) }

				// Broadcast app update
				if app := get_app_by_name(db, process) {
					app_json := json.encode(app)
					ws_msg := create_ws_message(app_update, '{"app": $app_json}')
					lock procMap {
						broadcast_to_clients(mut procMap, ws_msg)
					}
				}
			}
			.dbop {
				if debug { println("timeline op") }
				db.exec(sql_string) or { eprintln(sql_err) }
			}
			.longest {
				if debug { println("longest op") }
				sql db { update Apps set longest_session = duration where name == process } or { eprintln(db_err) }
			}
			.insert {
				if debug { println("insert: $process") }
				lock procMap { procMap.processes[process] = false }
			}
			.status {
				status := channel_data.status or { false }
				if debug { println("status update: $status") }

				lock procMap {
					procMap.processes[process] = status
					if status {
						procMap.tracking_status.current_app = process
					}
				}

				// Broadcast tracking status update
				status_update := rlock procMap {
					status_json := json.encode(procMap.tracking_status)
					create_ws_message(tracking_status_update, '{"status": $status_json}')
				}
				lock procMap {
					broadcast_to_clients(mut procMap, status_update)
				}
			}
			.broadcast {
				if ws_message := channel_data.ws_message {
					lock procMap {
						broadcast_to_clients(mut procMap, ws_message)
					}
				}
				if tracking_status := channel_data.tracking_status {
					lock procMap {
						procMap.tracking_status = tracking_status
						status_json := json.encode(tracking_status)
						ws_msg := create_ws_message(tracking_status_update, '{"status": $status_json}')
						broadcast_to_clients(mut procMap, ws_msg)
					}
				}
			}
		}
	}
}

fn monitor_process(process string, app Apps, debug bool, ch chan ChannelData) ! {
	if debug { println('"$process" is running.') }

	today := fn () string {
		now := time.now()
		return now.str().split(' ')[0]
	}

	init_launches := app.launches
	mut duration := app.duration or { 0 }
	longest_session := app.longest_session
	mut current_duration := 0
	mut launched := false

	mut channel_data := ChannelData {
		process: process,
		status: true
		operations: Operation.status
	}

	ch <- channel_data

	for is_process_running(process, debug) {
		time.sleep(60000 * time.millisecond)
		if launched == false {
			launches := init_launches or { 0 } + 1

			channel_data.launches = launches
			channel_data.operations = Operation.launches

			ch <- channel_data

			launched = true
		}

		duration++
		current_duration++

		if debug { println('Total Duration: ${duration}.') }
		if debug { println('Current session duration: ${current_duration}.') }

		channel_data.duration = duration
		channel_data.operations = Operation.apps

		ch <- channel_data

		now_str := today()

		channel_data.operations = Operation.dbop
		channel_data.sql_string = "CALL upsert_timeline('$process', '$now_str')"
		channel_data.sql_error = 'Could not update "Timeline" for app: ${process}.'

		ch <- channel_data
	}

	println('Session detail for app: "${channel_data.process}"')
	println('Total Duration: ${duration}.')
	println('Current session duration: ${current_duration}.')

	execute('notify-send "time_tracker" "process: \"${process}\" | current: ${current_duration} | total: ${duration}"')

	if current_duration > longest_session or { 0 } {
		channel_data.duration = current_duration
		channel_data.operations = Operation.longest

		ch <- channel_data

		now_str := today()

		channel_data.operations = Operation.dbop
		channel_data.sql_string = "UPDATE Apps SET longest_session_on = '$now_str' WHERE name = '$process'"
		channel_data.sql_error = 'Could not update app: ${process}.'

		ch <- channel_data

		println('New longest session for "$process" on: "$now_str" with duration: ${current_duration}.')
	}

	channel_data.operations = Operation.status
	channel_data.status = false

	ch <- channel_data
}

fn (shared procMap ProcMap) discovery(db pg.DB, debug bool, server_mode bool) ! {
	if debug {
		println('Starting discovery.')
	}

	ch := chan ChannelData{}

	spawn procMap.enhanced_db_service(db, debug, ch)


  spawn procMap.enhanced_socket_service(db, debug, ch)

	for {
		if rlock procMap { procMap.pause == true } {
			time.sleep(1000 * time.millisecond)
			continue
		}

		for process in rlock procMap { procMap.processes.keys() } {
			if is_process_running(process, debug) && rlock procMap { procMap.processes[process] == false } {
				if debug { println('Found running process "$process".') }

				execute('notify-send "time_tracker" "Started tracking \"$process\"')

				app_query := sql db { select from Apps where name == process } !

				app := app_query[0] or {
					eprintln('Could not find App: "$process".')
					continue
				}

				spawn monitor_process(process, app, debug, ch)
			}
		}

		time.sleep(1000 * time.millisecond)
	}
}

fn (shared procMap ProcMap) socket_service(ch chan ChannelData) ! {
	opt := socket.ServerOpt {}
	addr := net.AddrFamily.ip
	mut server := socket.new_server(addr, 6754, "/interop", opt)

	server.on_connect(fn(mut _ socket.ServerClient) !bool {
		return true
	}) !

	server.on_message(fn[mut procMap, ch] (mut _ socket.Client, msg &socket.Message) ! {
		payload := msg.payload

		if payload.len > 0 && payload.bytestr() == 'pause' {
			lock procMap {
				pause := procMap.pause
				println("Setting pause to: ${!pause}")
				execute('notify-send "time_tracker" "Setting pause to: ${!pause}"')
				procMap.pause = !pause
			}
		}

		insert_process := payload.bytestr().split(',')

		if payload.len > 0 && insert_process[0] == 'insert' {
			mut channel_data := ChannelData {
				process: insert_process[1],
				operations: Operation.insert
			}

			ch <- channel_data
		}
	})

	server.listen() !
}

db := pg.connect(pg.Config{
	host: '********'
	user: 'postgres'
	password: 'root'
	dbname: 'time_tracker'
}) or {
	println('Failed to connect.\n')
	println(err)
	exit(-1)
}

defer {
	db.close()
}

mut fp := flag.new_flag_parser(os.args)

fp.application('time-tracker-script')
fp.description('a small script to interface with TimeTracker.')
fp.usage_example('[options] <process>')
fp.skip_executable()

display := fp.bool('display', 0, false, 'Display data for process and return')
display_all := fp.bool('display-all', 0, false, 'Display data for process and return')
insert := fp.bool('insert', 0, false, 'Insert process if it is not already tracked')
debug := fp.bool('debug', 0, false, 'Print debug output')
pause := fp.bool('pause', 0, false, 'Pause/Unpause tracking')

if pause {
	opt := socket.ClientOpt {}
	mut client := socket.new_client("ws://127.0.0.1:6754/interop", opt) !
	client.connect() !
	client.write_string("pause") !
	exit(0)
}

if display_all {
	app_query := sql db {
		select from Apps order by id
	} !

	println('Time data for all apps. \n')
	println(app_query)
	exit(0)
}

args := fp.finalize() or {
	eprintln(err)
	println(fp.usage())
	exit(-1)
}

all_apps := sql db { select from Apps order by id } !

process := args.join_lines()

if display {
	fp.limit_free_args(1, 1) !

	app_query := sql db { select from Apps where name == process } !

	app := app_query[0] or {
		eprintln('Could not find App: "$process".')
		exit(-1)
	}

	println('Time data for app: "$process". \n')
	println(app)
	exit(0)
}

if insert {
	fp.limit_free_args(1, 1) !

	contains := process in all_apps.map(fn (a Apps) string { return a.name or { "" } })

	if !contains {
		new_id := all_apps.last().id + 1

		db.exec("INSERT INTO Apps (id, name) VALUES ($new_id, '$process')") or {
			eprintln('Could not insert into "Apps" for app: ${process}.')
			exit(-1)
		}

		opt := socket.ClientOpt {}
		mut client := socket.new_client("ws://127.0.0.1:6754/interop", opt) !
		client.connect() !
		client.write_string("insert,$process") !

		if debug { println('App: "$process" has been inserted.') }
	} else {
		if debug { println('App: "$process" already exists.') }
	}

	exit(0)
}

mut processes := map[string]bool{}

for app in all_apps {
	name := app.name or { continue }
	processes[name] = false
}

shared proc_map := &ProcMap{
	processes: processes
	tracking_status: TrackingStatus{
		is_tracking: false
		is_paused: false
		current_session_duration: 0
	}
	connected_clients: []
}

tt_instances := execute('pgrep time-tracker').output.trim_space_right()

if tt_instances.split('\n').any(it != os.getpid().str()) {
	println('There is already an instance of "time-tracker" running. Closing ...')
	exit(0)
}


proc_map.discovery(db, debug, false) !


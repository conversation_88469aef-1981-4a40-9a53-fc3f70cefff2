import 'dart:async';
import '../models/app_model.dart';
import '../services/websocket_service.dart';

abstract class TimeTrackerRepository {
  Future<List<AppModel>> getAllApps();
  Future<AppModel?> getAppByName(String name);
  Future<void> insertApp(String name);
  Future<void> deleteApp(int appId);
  Future<List<TimelineModel>> getTimeline({DateTime? startDate, DateTime? endDate, int? appId});
  Future<Map<int, int>> getSessionCounts();
  Future<TrackingStatus> getTrackingStatus();
  Future<void> startTracking();
  Future<void> stopTracking();
  Future<void> pauseTracking();
  Future<void> resumeTracking();
  Future<List<AppStatistics>> getStatistics({DateTime? startDate, DateTime? endDate});

  // Checkpoint methods
  Future<List<CheckpointModel>> getCheckpoints(int appId);
  Future<CheckpointModel?> getActiveCheckpoint(int appId);
  Future<void> createCheckpoint(String name, String? description, DateTime validFrom, String color, int appId);
  Future<void> setActiveCheckpoint(int checkpointId, int appId);
  Future<void> clearActiveCheckpoint(int appId);
  Future<void> deleteCheckpoint(int checkpointId, int appId);
  Future<List<CheckpointDurationModel>> getCheckpointStats(int checkpointId);

  Stream<TrackingStatus> get trackingStatusStream;
  Stream<AppModel> get appUpdateStream;
  Stream<Map<String, dynamic>> get sessionUpdateStream;
  Stream<List<CheckpointModel>> get checkpointsStream;
  Stream<CheckpointModel?> get activeCheckpointStream;

  Future<void> connect();
  Future<void> disconnect();
}

class WebSocketTimeTrackerRepository implements TimeTrackerRepository {
  final WebSocketService _webSocketService;
  final Map<String, Completer<List<AppModel>>> _appsCompleters = {};
  final Map<String, Completer<List<CheckpointModel>>> _checkpointsCompleters = {};
  final Map<String, Completer<CheckpointModel?>> _activeCheckpointCompleters = {};
  final Map<String, Completer<List<TimelineModel>>> _timelineCompleters = {};
  final Map<String, Completer<Map<int, int>>> _sessionCountsCompleters = {};
  final Map<String, Completer<TrackingStatus>> _trackingStatusCompleters = {};
  final Map<String, Completer<List<AppStatistics>>> _statisticsCompleters = {};

  late StreamSubscription _appsSubscription;
  late StreamSubscription _checkpointsSubscription;
  late StreamSubscription _activeCheckpointSubscription;
  late StreamSubscription _timelineSubscription;
  late StreamSubscription _sessionCountsSubscription;
  late StreamSubscription _trackingStatusSubscription;
  late StreamSubscription _statisticsSubscription;

  WebSocketTimeTrackerRepository({
    required WebSocketService webSocketService,
  }) : _webSocketService = webSocketService {
    _setupStreamListeners();
  }

  void _setupStreamListeners() {
    _appsSubscription = _webSocketService.appsListStream.listen((apps) {
      _completeAllPending(_appsCompleters, apps);
    });

    _checkpointsSubscription = _webSocketService.checkpointsStream.listen((checkpoints) {
      _completeAllPending(_checkpointsCompleters, checkpoints);
    });

    _activeCheckpointSubscription = _webSocketService.activeCheckpointStream.listen((activeCheckpoint) {
      _completeAllPending(_activeCheckpointCompleters, activeCheckpoint);
    });

    _timelineSubscription = _webSocketService.timelineStream.listen((timeline) {
      _completeAllPending(_timelineCompleters, timeline);
    });

    _sessionCountsSubscription = _webSocketService.sessionCountsStream.listen((counts) {
      _completeAllPending(_sessionCountsCompleters, counts);
    });

    _trackingStatusSubscription = _webSocketService.trackingStatusStream.listen((status) {
      _completeAllPending(_trackingStatusCompleters, status);
    });

    _statisticsSubscription = _webSocketService.statisticsStream.listen((statistics) {
      _completeAllPending(_statisticsCompleters, statistics);
    });
  }

  void _completeAllPending<T>(Map<String, Completer<T>> completers, T data) {
    final pendingCompleters = List<Completer<T>>.from(completers.values);
    completers.clear();
    for (final completer in pendingCompleters) {
      if (!completer.isCompleted) {
        completer.complete(data);
      }
    }
  }

  Future<T> _createRequest<T>(
    Map<String, Completer<T>> completers,
    Future<void> Function() requestFunction,
  ) async {
    final requestId = DateTime.now().millisecondsSinceEpoch.toString();
    final completer = Completer<T>();
    completers[requestId] = completer;

    try {
      await requestFunction();
      return await completer.future.timeout(
        const Duration(seconds: 10),
        onTimeout: () {
          completers.remove(requestId);
          throw TimeoutException('Request timed out', const Duration(seconds: 10));
        },
      );
    } catch (e) {
      completers.remove(requestId);
      rethrow;
    }
  }

  @override
  Future<List<AppModel>> getAllApps() async {
    return _createRequest(_appsCompleters, () => _webSocketService.getAllApps());
  }

  @override
  Future<AppModel?> getAppByName(String name) async {
    await _webSocketService.getAppByName(name);
    return null; // This would need specific handling for single app responses
  }

  @override
  Future<void> insertApp(String name) => _webSocketService.insertApp(name);

  @override
  Future<void> deleteApp(int appId) => _webSocketService.deleteApp(appId);

  @override
  Future<List<TimelineModel>> getTimeline({
    DateTime? startDate,
    DateTime? endDate,
    int? appId,
  }) async {
    return _createRequest(_timelineCompleters, () => _webSocketService.getTimeline(
      startDate: startDate,
      endDate: endDate,
      appId: appId,
    ));
  }

  @override
  Future<Map<int, int>> getSessionCounts() async {
    return _createRequest(_sessionCountsCompleters, () => _webSocketService.getSessionCounts());
  }

  @override
  Future<TrackingStatus> getTrackingStatus() async {
    return _createRequest(_trackingStatusCompleters, () => _webSocketService.getTrackingStatus());
  }

  @override
  Future<void> startTracking() => _webSocketService.startTracking();

  @override
  Future<void> stopTracking() => _webSocketService.stopTracking();

  @override
  Future<void> pauseTracking() => _webSocketService.pauseTracking();

  @override
  Future<void> resumeTracking() => _webSocketService.resumeTracking();

  @override
  Future<List<AppStatistics>> getStatistics({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    return _createRequest(_statisticsCompleters, () => _webSocketService.getStatistics(
      startDate: startDate,
      endDate: endDate,
    ));
  }

  @override
  Stream<TrackingStatus> get trackingStatusStream => _webSocketService.trackingStatusStream;

  @override
  Stream<AppModel> get appUpdateStream => _webSocketService.appUpdateStream;

  @override
  Stream<Map<String, dynamic>> get sessionUpdateStream => _webSocketService.sessionUpdateStream;

  Stream<List<AppModel>> get appsListStream => _webSocketService.appsListStream;
  Stream<List<TimelineModel>> get timelineStream => _webSocketService.timelineStream;
  Stream<List<AppStatistics>> get statisticsStream => _webSocketService.statisticsStream;
  Stream<Map<int, int>> get sessionCountsStream => _webSocketService.sessionCountsStream;
  Stream<bool> get connectionStateStream => _webSocketService.connectionStateStream;
  Stream<List<CheckpointWithStatus>> get checkpointsWithStatusStream => _webSocketService.checkpointsWithStatusStream;

  @override
  Future<void> connect() async {
    await _webSocketService.connect();
  }

  @override
  Future<void> disconnect() async {
    await _webSocketService.disconnect();
  }

  void dispose() {
    _appsSubscription.cancel();
    _checkpointsSubscription.cancel();
    _activeCheckpointSubscription.cancel();
    _timelineSubscription.cancel();
    _sessionCountsSubscription.cancel();
    _trackingStatusSubscription.cancel();
    _statisticsSubscription.cancel();
    _webSocketService.dispose();
  }

  bool get isConnected => _webSocketService.isConnected;

  @override
  Future<List<CheckpointModel>> getCheckpoints(int appId) async {
    return _createRequest(_checkpointsCompleters, () => _webSocketService.getCheckpoints(appId));
  }

  @override
  Future<CheckpointModel?> getActiveCheckpoint(int appId) async {
    // Use the active checkpoint stream to get the current active checkpoint
    return _createRequest(_activeCheckpointCompleters, () => _webSocketService.getCheckpoints(appId));
  }

  @override
  Future<void> createCheckpoint(String name, String? description, DateTime validFrom, String color, int appId) => 
      _webSocketService.createCheckpoint(name, description, validFrom, color, appId);

  @override
  Future<void> setActiveCheckpoint(int checkpointId, int appId) => _webSocketService.setActiveCheckpoint(checkpointId, appId);

  @override
  Future<void> clearActiveCheckpoint(int appId) => _webSocketService.clearActiveCheckpoint(appId);

  @override
  Future<void> deleteCheckpoint(int checkpointId, int appId) => _webSocketService.deleteCheckpoint(checkpointId, appId);

  @override
  Future<List<CheckpointDurationModel>> getCheckpointStats(int checkpointId) async {
    await _webSocketService.getCheckpointStats(checkpointId);
    return []; // This would need specific handling for checkpoint stats
  }

  @override
  Stream<List<CheckpointModel>> get checkpointsStream => _webSocketService.checkpointsStream;

  @override
  Stream<CheckpointModel?> get activeCheckpointStream => _webSocketService.activeCheckpointStream;
}

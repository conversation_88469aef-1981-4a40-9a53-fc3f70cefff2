import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'app/theme/app_theme.dart';
import 'presentation/providers/theme_provider.dart';
import 'presentation/providers/app_providers.dart';
import 'presentation/pages/home_page.dart';
import 'core/constants/api_constants.dart';
void main() {
  runApp(const ProviderScope(child: TimeTrackerApp()));
}

class TimeTrackerApp extends ConsumerWidget {
  const TimeTrackerApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final themeMode = ref.watch(themeModeProvider);

    // Watch the settings change provider to activate it
    ref.watch(settingsChangeProvider);

    const baseDesignWidth = 1280.0; // where spacing constants look best
    const baseDesignHeight = 720.0;
    const minScale = 0.5;
    const maxScale = 0.75;

    final screenWidth = MediaQuery.sizeOf(context).width;
    final scale = (screenWidth / baseDesignWidth).clamp(minScale, maxScale);

    final designSize = Size(screenWidth / scale, baseDesignHeight);

    return ScreenUtilInit(
      designSize: designSize,
      minTextAdapt: true,
      splitScreenMode: true,
      builder: (context, child) {
        return MaterialApp(
          title: AppConstants.appName,
          theme: AppTheme.lightTheme,
          darkTheme: AppTheme.darkTheme,
          themeMode: themeMode,
          home: const HomePage(),
          debugShowCheckedModeBanner: false,
        );
      },
    );
  }
}



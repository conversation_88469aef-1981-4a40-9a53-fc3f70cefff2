import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../data/models/app_model.dart';
import '../../../core/constants/ui_constants.dart';
import '../../../app/theme/app_theme.dart';
import '../../providers/app_providers.dart';

class AppStatsRow extends ConsumerWidget {
  final AppModel app;

  const AppStatsRow({
    super.key,
    required this.app,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            'Launches',
            '${app.launches ?? 0}',
            Icons.launch,
            isDark ? AppTheme.secondaryColor : AppTheme.secondaryDark,
            isDark,
            theme,
          ),
        ),
        SizedBox(width: UIConstants.spacingM.w),
        Expanded(
          child: _buildStatCard(
            'Avg Session',
            _formatDuration(_calculateAverageSession()),
            Icons.timer,
            isDark ? AppTheme.infoColor : AppTheme.primaryDark,
            isDark,
            theme,
          ),
        ),
        SizedBox(width: UIConstants.spacingM.w),
        Expanded(
          child: _buildStatCard(
            'Last Played',
            _formatLastPlayed(ref),
            Icons.schedule,
            isDark ? AppTheme.warningColor : AppTheme.accentDark,
            isDark,
            theme,
          ),
        ),
      ],
    );
  }

  Widget _buildStatCard(
    String label,
    String value,
    IconData icon,
    Color color,
    bool isDark,
    ThemeData theme,
  ) {
    return Container(
      padding: EdgeInsets.all(UIConstants.spacingM.w),
      decoration: BoxDecoration(
        color: isDark ? theme.cardBg : theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(UIConstants.radiusM),
        border: Border.all(
          color: color.withOpacity(0.3),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: color.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: color,
            size: UIConstants.iconL,
          ),
          SizedBox(height: UIConstants.spacingS.h),
          Text(
            value,
            style: theme.textTheme.titleLarge?.copyWith(
              color: color,
              fontWeight: FontWeight.w700,
            ),
          ),
          SizedBox(height: UIConstants.spacingXS.h),
          Text(
            label,
            style: theme.textTheme.bodySmall?.copyWith(
              color: isDark ? AppTheme.neutralGray400 : AppTheme.neutralGray600,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  int _calculateAverageSession() {
    final totalDuration = app.duration ?? 0;
    final launches = app.launches ?? 1;
    return launches > 0 ? totalDuration ~/ launches : 0;
  }

  String _formatLastPlayed(WidgetRef ref) {
    // Get all timeline data for this app to find the most recent session
    final allTimeTimeline = ref.watch(allTimeTimelineProvider);

    return allTimeTimeline.when(
      data: (timeline) {
        // Filter timeline for this specific app and find the most recent session
        final appSessions = timeline
            .where((session) => session.appId == app.id && session.date != null)
            .toList();

        if (appSessions.isEmpty) return 'Never';

        // Sort by date to get the most recent session
        appSessions.sort((a, b) => b.date!.compareTo(a.date!));
        final mostRecentSession = appSessions.first;

        final now = DateTime.now();
        final difference = now.difference(mostRecentSession.date!).inDays;

        return switch (difference) {
          0 => 'Today',
          1 => 'Yesterday',
          < 7 => '${difference}d ago',
          < 30 => '${difference ~/ 7}w ago',
          < 365 => '${difference ~/ 30}mo ago',
          _ => _formatActualDate(mostRecentSession.date!),
        };
      },
      loading: () => '...',
      error: (_, __) => 'Never',
    );
  }

  String _formatActualDate(DateTime date) {
    const months = [
      'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ];

    return '${months[date.month - 1]} ${date.day}, ${date.year}';
  }

  String _formatDuration(int minutes) {
    return minutes < 60
      ? '${minutes}m'
      : '${minutes ~/ 60}h ${minutes % 60}m';
  }
}
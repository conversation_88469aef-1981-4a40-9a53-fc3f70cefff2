import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../data/models/app_model.dart';
import '../../providers/checkpoint_providers.dart';
import '../../../core/constants/ui_constants.dart';
import '../../../app/theme/app_theme.dart';

class CheckpointSelector extends ConsumerWidget {
  final AppModel app;
  final CheckpointModel? selectedCheckpoint;
  final ValueChanged<CheckpointModel?> onChanged;

  const CheckpointSelector({
    super.key,
    required this.app,
    required this.selectedCheckpoint,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    final checkpointsAsync = ref.watch(checkpointsForAppProvider(app.id));

    return checkpointsAsync.when(
      data: (checkpoints) => _buildCheckpointSelector(checkpoints, theme, isDark),
      loading: () => _buildLoadingSelector(theme, isDark),
      error: (error, stack) => _buildErrorSelector(theme, isDark),
    );
  }

  Widget _buildCheckpointSelector(List<CheckpointWithStatus> checkpoints, ThemeData theme, bool isDark) {
    // Deduplicate checkpoints by ID to prevent dropdown errors
    final Map<int, CheckpointWithStatus> uniqueCheckpoints = {};
    for (final checkpoint in checkpoints) {
      uniqueCheckpoints[checkpoint.id] = checkpoint;
    }
    final deduplicatedCheckpoints = uniqueCheckpoints.values.toList();

    // Validate that selectedCheckpoint is in the available list
    CheckpointModel? validSelectedCheckpoint = selectedCheckpoint;
    if (selectedCheckpoint != null) {
      final isValidSelection = deduplicatedCheckpoints.any(
        (cws) => cws.checkpoint.id == selectedCheckpoint!.id
      );
      if (!isValidSelection) {
        validSelectedCheckpoint = null;
      }
    }

    return Container(
      padding: EdgeInsets.symmetric(horizontal: UIConstants.spacingM.w),
      decoration: BoxDecoration(
        color: isDark ? theme.cardBg : theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(UIConstants.radiusM),
        border: Border.all(
          color: isDark ? AppTheme.neutralGray600 : AppTheme.neutralGray400,
          width: 1,
        ),
      ),
      child: DropdownButton<CheckpointModel?>(
        value: validSelectedCheckpoint,
        hint: Text(
          'All Checkpoints',
          style: theme.textTheme.bodyMedium?.copyWith(
            color: isDark ? AppTheme.neutralGray300 : AppTheme.neutralGray700,
          ),
        ),
        underline: const SizedBox(),
        isExpanded: true,
        dropdownColor: isDark ? theme.cardBg : theme.colorScheme.surface,
        style: theme.textTheme.bodyMedium?.copyWith(
          color: isDark ? AppTheme.neutralGray100 : AppTheme.neutralGray900,
        ),
        items: [
          DropdownMenuItem<CheckpointModel?>(
            value: null,
            child: Row(
              children: [
                Icon(
                  Icons.all_inclusive,
                  size: UIConstants.iconS,
                  color: isDark ? AppTheme.neutralGray400 : AppTheme.neutralGray600,
                ),
                SizedBox(width: UIConstants.spacingS.w),
                Text('All Checkpoints'),
              ],
            ),
          ),
          ...deduplicatedCheckpoints.map((checkpointWithStatus) {
            final checkpoint = checkpointWithStatus.checkpoint;
            return DropdownMenuItem<CheckpointModel?>(
              value: checkpoint,
              child: Row(
                children: [
                  Container(
                    width: 12.w,
                    height: 12.h,
                    decoration: BoxDecoration(
                      color: Color(int.parse(checkpoint.color.replaceFirst('#', '0xFF'))),
                      shape: BoxShape.circle,
                    ),
                  ),
                  SizedBox(width: UIConstants.spacingS.w),
                  Expanded(
                    child: Text(
                      checkpoint.name,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        fontWeight: checkpointWithStatus.isActive ? FontWeight.w600 : FontWeight.normal,
                      ),
                    ),
                  ),
                  if (checkpointWithStatus.isActive)
                    Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: UIConstants.spacingXS.w,
                        vertical: 2.h,
                      ),
                      decoration: BoxDecoration(
                        color: AppTheme.successColor.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        'ACTIVE',
                        style: theme.textTheme.labelSmall?.copyWith(
                          color: AppTheme.successColor,
                          fontWeight: FontWeight.w600,
                          fontSize: 8.sp,
                        ),
                      ),
                    ),
                ],
              ),
            );
          }),
        ],
        onChanged: onChanged,
      ),
    );
  }

  Widget _buildLoadingSelector(ThemeData theme, bool isDark) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: UIConstants.spacingM.w,
        vertical: UIConstants.spacingM.h,
      ),
      decoration: BoxDecoration(
        color: isDark ? theme.cardBg : theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(UIConstants.radiusM),
        border: Border.all(
          color: isDark ? AppTheme.neutralGray600 : AppTheme.neutralGray400,
          width: 1,
        ),
      ),
      child: Row(
        children: [
          SizedBox(
            width: 16.w,
            height: 16.h,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(
                isDark ? AppTheme.primaryColor : theme.colorScheme.primary,
              ),
            ),
          ),
          SizedBox(width: UIConstants.spacingS.w),
          Text(
            'Loading checkpoints...',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: isDark ? AppTheme.neutralGray300 : AppTheme.neutralGray700,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorSelector(ThemeData theme, bool isDark) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: UIConstants.spacingM.w,
        vertical: UIConstants.spacingM.h,
      ),
      decoration: BoxDecoration(
        color: isDark ? theme.cardBg : theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(UIConstants.radiusM),
        border: Border.all(
          color: isDark ? AppTheme.neutralGray600 : AppTheme.neutralGray400,
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.error_outline,
            size: UIConstants.iconS,
            color: theme.colorScheme.error,
          ),
          SizedBox(width: UIConstants.spacingS.w),
          Text(
            'Failed to load checkpoints',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.error,
            ),
          ),
        ],
      ),
    );
  }
}
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../data/models/app_model.dart';
import '../../providers/checkpoint_providers.dart';
import '../../../core/constants/ui_constants.dart';
import '../../../app/theme/app_theme.dart';
import '../../../util/time_util.dart';

class CheckpointStatsCard extends ConsumerWidget {
  final CheckpointModel checkpoint;
  final String appId;

  const CheckpointStatsCard({
    super.key,
    required this.checkpoint,
    required this.appId,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    final checkpointStatsAsync = ref.watch(checkpointStatsProvider(checkpoint.id));

    return Container(
      padding: EdgeInsets.all(UIConstants.spacingL.w),
      decoration: BoxDecoration(
        color: isDark ? theme.cardBg : theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(UIConstants.radiusL),
        border: Border.all(
          color: Color(int.parse(checkpoint.color.replaceFirst('#', '0xFF'))).withOpacity(0.3),
          width: 2,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 16.w,
                height: 16.h,
                decoration: BoxDecoration(
                  color: Color(int.parse(checkpoint.color.replaceFirst('#', '0xFF'))),
                  shape: BoxShape.circle,
                ),
              ),
              SizedBox(width: UIConstants.spacingS.w),
              Expanded(
                child: Text(
                  checkpoint.name,
                  style: theme.textTheme.titleMedium?.copyWith(
                    color: isDark ? AppTheme.neutralGray100 : AppTheme.neutralGray900,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          if (checkpoint.description != null) ...[
            SizedBox(height: UIConstants.spacingS.h),
            Text(
              checkpoint.description!,
              style: theme.textTheme.bodySmall?.copyWith(
                color: isDark ? AppTheme.neutralGray400 : AppTheme.neutralGray600,
              ),
            ),
          ],
          SizedBox(height: UIConstants.spacingM.h),
          checkpointStatsAsync.when(
            data: (stats) => _buildStatsContent(stats, theme, isDark),
            loading: () => _buildLoadingContent(theme, isDark),
            error: (error, stack) => _buildErrorContent(theme, isDark),
          ),
        ],
      ),
    );
  }

  Widget _buildStatsContent(List<CheckpointDurationModel> stats, ThemeData theme, bool isDark) {
    if (stats.isEmpty) {
      return _buildEmptyContent(theme, isDark);
    }

    final totalDuration = stats.fold<int>(0, (sum, stat) => sum + stat.duration);
    final totalSessions = stats.fold<int>(0, (sum, stat) => sum + stat.sessionsCount);
    final averageSession = totalSessions > 0 ? totalDuration / totalSessions : 0;

    return Row(
      children: [
        Expanded(
          child: _buildStatItem(
            'Total Time',
            TimeUtil.formatDurationFromMinutes(totalDuration),
            Icons.access_time,
            theme,
            isDark,
          ),
        ),
        SizedBox(width: UIConstants.spacingM.w),
        Expanded(
          child: _buildStatItem(
            'Sessions',
            totalSessions.toString(),
            Icons.play_circle_outline,
            theme,
            isDark,
          ),
        ),
        SizedBox(width: UIConstants.spacingM.w),
        Expanded(
          child: _buildStatItem(
            'Avg Session',
            TimeUtil.formatDurationFromMinutes(averageSession.round()),
            Icons.trending_up,
            theme,
            isDark,
          ),
        ),
      ],
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon, ThemeData theme, bool isDark) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              icon,
              size: UIConstants.iconXS,
              color: isDark ? AppTheme.neutralGray400 : AppTheme.neutralGray600,
            ),
            SizedBox(width: UIConstants.spacingXS.w),
            Text(
              label,
              style: theme.textTheme.bodySmall?.copyWith(
                color: isDark ? AppTheme.neutralGray400 : AppTheme.neutralGray600,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        SizedBox(height: UIConstants.spacingXS.h),
        Text(
          value,
          style: theme.textTheme.titleSmall?.copyWith(
            color: isDark ? AppTheme.neutralGray100 : AppTheme.neutralGray900,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  Widget _buildLoadingContent(ThemeData theme, bool isDark) {
    return Row(
      children: [
        SizedBox(
          width: 16.w,
          height: 16.h,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(
              Color(int.parse(checkpoint.color.replaceFirst('#', '0xFF'))),
            ),
          ),
        ),
        SizedBox(width: UIConstants.spacingS.w),
        Text(
          'Loading statistics...',
          style: theme.textTheme.bodyMedium?.copyWith(
            color: isDark ? AppTheme.neutralGray300 : AppTheme.neutralGray700,
          ),
        ),
      ],
    );
  }

  Widget _buildErrorContent(ThemeData theme, bool isDark) {
    return Row(
      children: [
        Icon(
          Icons.error_outline,
          size: UIConstants.iconS,
          color: theme.colorScheme.error,
        ),
        SizedBox(width: UIConstants.spacingS.w),
        Text(
          'Failed to load statistics',
          style: theme.textTheme.bodyMedium?.copyWith(
            color: theme.colorScheme.error,
          ),
        ),
      ],
    );
  }

  Widget _buildEmptyContent(ThemeData theme, bool isDark) {
    return Row(
      children: [
        Icon(
          Icons.info_outline,
          size: UIConstants.iconS,
          color: isDark ? AppTheme.neutralGray400 : AppTheme.neutralGray600,
        ),
        SizedBox(width: UIConstants.spacingS.w),
        Text(
          'No statistics available for this checkpoint',
          style: theme.textTheme.bodyMedium?.copyWith(
            color: isDark ? AppTheme.neutralGray400 : AppTheme.neutralGray600,
          ),
        ),
      ],
    );
  }
}
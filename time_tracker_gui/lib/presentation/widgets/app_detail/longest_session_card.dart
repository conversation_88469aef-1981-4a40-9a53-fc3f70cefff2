import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../data/models/app_model.dart';
import '../../../core/constants/ui_constants.dart';
import '../../../app/theme/app_theme.dart';
import '../../../util/time_util.dart';
class LongestSessionCard extends StatelessWidget {
  final AppModel app;

  const LongestSessionCard({
    super.key,
    required this.app,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    final primaryColor = isDark ? AppTheme.primaryColor : theme.colorScheme.primary;

    return Container(
      padding: EdgeInsets.all(UIConstants.spacingL.w),
      decoration: BoxDecoration(
        color: isDark ? theme.cardBg : theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(UIConstants.radiusL),
        border: Border.all(
          color: primaryColor.withOpacity(0.3),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: primaryColor.withOpacity(0.1),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          _buildIcon(primaryColor),
          SizedBox(width: UIConstants.spacingL.w),
          Expanded(child: _buildContent(theme, isDark, primaryColor)),
          _buildDateInfo(theme, isDark),
        ],
      ),
    );
  }

  Widget _buildIcon(Color primaryColor) {
    return Container(
      padding: EdgeInsets.all(UIConstants.spacingM.w),
      decoration: BoxDecoration(
        color: primaryColor.withOpacity(0.2),
        borderRadius: BorderRadius.circular(UIConstants.radiusM),
      ),
      child: Icon(
        Icons.emoji_events,
        color: primaryColor,
        size: UIConstants.iconL,
      ),
    );
  }

  Widget _buildContent(ThemeData theme, bool isDark, Color primaryColor) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Longest Gaming Session (All Time)',
          style: theme.textTheme.titleMedium?.copyWith(
            color: isDark ? AppTheme.neutralGray100 : AppTheme.neutralGray900,
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: UIConstants.spacingXS.h),
        Text(
          TimeUtil.formatMinutes(app.longestSession ?? 0),
          style: theme.textTheme.headlineSmall?.copyWith(
            color: primaryColor,
            fontWeight: FontWeight.w700,
          ),
        ),
      ],
    );
  }

  Widget _buildDateInfo(ThemeData theme, bool isDark) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        Icon(
          Icons.calendar_today,
          color: isDark ? AppTheme.neutralGray400 : AppTheme.neutralGray600,
          size: UIConstants.iconS,
        ),
        SizedBox(height: UIConstants.spacingXS.h),
        Text(
          _formatDate(app.longestSessionOn),
          style: theme.textTheme.bodySmall?.copyWith(
            color: isDark ? AppTheme.neutralGray400 : AppTheme.neutralGray600,
          ),
        ),
      ],
    );
  }

  String _formatDate(DateTime? date) {
    return date?.toString().split(' ')[0] ?? 'Unknown';
  }
}
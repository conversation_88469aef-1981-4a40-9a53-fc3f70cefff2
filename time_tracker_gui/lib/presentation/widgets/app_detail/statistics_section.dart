import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../data/models/app_model.dart';
import '../../providers/app_providers.dart';
import '../../../core/constants/ui_constants.dart';
import '../../../app/theme/app_theme.dart';
import '../../../util/time_util.dart';
import 'app_detail_enums.dart';
import 'session_service.dart';

class StatisticsSection extends ConsumerWidget {
  final String appId;
  final TimeRange selectedTimeRange;
  final CheckpointModel? selectedCheckpoint;

  const StatisticsSection({
    super.key,
    required this.appId,
    required this.selectedTimeRange,
    this.selectedCheckpoint,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final timeline = ref.watch(timelineProvider);
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      padding: EdgeInsets.all(UIConstants.spacingL.w),
      decoration: BoxDecoration(
        color: isDark ? theme.cardBg : theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(UIConstants.radiusL),
        border: Border.all(
          color: selectedCheckpoint != null
            ? Color(int.parse(selectedCheckpoint!.color.replaceFirst('#', '0xFF'))).withOpacity(0.3)
            : isDark ? AppTheme.neutralGray700 : AppTheme.neutralGray300,
          width: selectedCheckpoint != null ? 2 : 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(theme, isDark),
          SizedBox(height: UIConstants.spacingL.h),
          _buildStatisticsContent(timeline, theme, isDark),
        ],
      ),
    );
  }

  Widget _buildHeader(ThemeData theme, bool isDark) {
    final filterDesc = SessionService.getFilterDescription(selectedTimeRange, selectedCheckpoint);

    return Row(
      children: [
        if (selectedCheckpoint != null) ...[
          Container(
            width: 16.w,
            height: 16.h,
            decoration: BoxDecoration(
              color: Color(int.parse(selectedCheckpoint!.color.replaceFirst('#', '0xFF'))),
              shape: BoxShape.circle,
            ),
          ),
          SizedBox(width: UIConstants.spacingS.w),
        ],
        Expanded(
          child: Text(
            'Usage Statistics - $filterDesc',
            style: theme.textTheme.titleLarge?.copyWith(
              color: isDark ? AppTheme.neutralGray100 : AppTheme.neutralGray900,
              fontWeight: FontWeight.w700,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildStatisticsContent(AsyncValue<List<TimelineModel>> timeline, ThemeData theme, bool isDark) {
    return timeline.when(
      data: (timelineData) => _buildStatisticsData(timelineData, theme, isDark),
      loading: () => SizedBox(
        height: 200.h,
        child: const Center(child: CircularProgressIndicator()),
      ),
      error: (error, _) => _buildErrorState('Failed to load statistics', isDark, theme),
    );
  }

  Widget _buildStatisticsData(List<TimelineModel> timelineData, ThemeData theme, bool isDark) {
    final appSessions = timelineData.where((session) => session.appId.toString() == appId).toList();
    final stats = SessionService.calculateUsageStats(
      appSessions,
      selectedTimeRange,
      checkpoint: selectedCheckpoint,
    );

    return stats.totalSessions == 0
      ? _buildEmptyStatsState(isDark, theme)
      : _buildStatsGrid(stats, isDark, theme);
  }

  Widget _buildEmptyStatsState(bool isDark, ThemeData theme) {
    final filterDesc = SessionService.getFilterDescription(selectedTimeRange, selectedCheckpoint);

    return SizedBox(
      height: 200.h,
      child: Container(
        decoration: BoxDecoration(
          color: isDark
            ? theme.darkBg.withOpacity(0.5)
            : theme.colorScheme.surfaceContainerHighest.withOpacity(0.3),
          borderRadius: BorderRadius.circular(UIConstants.radiusM),
          border: Border.all(
            color: isDark
              ? AppTheme.neutralGray700.withOpacity(0.5)
              : AppTheme.neutralGray400.withOpacity(0.3),
          ),
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.analytics_outlined,
                size: UIConstants.iconXL,
                color: isDark ? AppTheme.neutralGray600 : AppTheme.neutralGray500,
              ),
              SizedBox(height: UIConstants.spacingM.h),
              Text(
                'No data for $filterDesc',
                style: theme.textTheme.bodyLarge?.copyWith(
                  color: isDark ? AppTheme.neutralGray400 : AppTheme.neutralGray600,
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: UIConstants.spacingS.h),
              Text(
                selectedCheckpoint != null
                  ? 'Play some games with this checkpoint to see statistics'
                  : 'Play some games to see statistics here',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: isDark ? AppTheme.neutralGray500 : AppTheme.neutralGray600,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatsGrid(UsageStatistics stats, bool isDark, ThemeData theme) {
    final primaryColor = selectedCheckpoint != null
      ? Color(int.parse(selectedCheckpoint!.color.replaceFirst('#', '0xFF')))
      : (isDark ? AppTheme.primaryColor : theme.colorScheme.primary);

    final secondaryColor = selectedCheckpoint != null
      ? primaryColor.withOpacity(0.8)
      : (isDark ? AppTheme.secondaryColor : AppTheme.secondaryDark);

    final infoColor = selectedCheckpoint != null
      ? primaryColor.withOpacity(0.7)
      : (isDark ? AppTheme.infoColor : AppTheme.primaryDark);

    final warningColor = selectedCheckpoint != null
      ? primaryColor.withOpacity(0.6)
      : (isDark ? AppTheme.warningColor : AppTheme.accentDark);

    return Column(
      children: [
        _buildStatsRow([
          _StatItem(
            label: 'Total Sessions',
            value: '${stats.totalSessions}',
            icon: Icons.play_circle_outline,
            color: primaryColor,
          ),
          _StatItem(
            label: 'Total Time',
            value: TimeUtil.formatMinutes(stats.totalDuration),
            icon: Icons.schedule,
            color: secondaryColor,
          ),
        ], isDark, theme),
        SizedBox(height: UIConstants.spacingM.h),
        _buildStatsRow([
          _StatItem(
            label: 'Average Session',
            value: TimeUtil.formatMinutes(stats.averageSession),
            icon: Icons.timer_outlined,
            color: infoColor,
          ),
          _StatItem(
            label: 'Daily Average',
            value: TimeUtil.formatMinutes(stats.dailyAverage),
            icon: Icons.today_outlined,
            color: warningColor,
          ),
        ], isDark, theme),
        SizedBox(height: UIConstants.spacingM.h),
        _buildLongestSessionHighlight(stats, isDark, theme, primaryColor),
      ],
    );
  }

  Widget _buildStatsRow(List<_StatItem> items, bool isDark, ThemeData theme) {
    return Row(
      children: items.map((item) => Expanded(
        child: Padding(
          padding: EdgeInsets.only(
            right: item == items.last ? 0 : UIConstants.spacingM.w,
          ),
          child: _buildStatItem(item, isDark, theme),
        ),
      )).toList(),
    );
  }

  Widget _buildStatItem(_StatItem item, bool isDark, ThemeData theme) {
    return Container(
      padding: EdgeInsets.all(UIConstants.spacingM.w),
      decoration: BoxDecoration(
        color: item.color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(UIConstants.radiusM),
        border: Border.all(
          color: item.color.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Icon(
            item.icon,
            color: item.color,
            size: UIConstants.iconM,
          ),
          SizedBox(height: UIConstants.spacingS.h),
          Text(
            item.value,
            style: theme.textTheme.titleMedium?.copyWith(
              color: item.color,
              fontWeight: FontWeight.w700,
            ),
          ),
          SizedBox(height: UIConstants.spacingXS.h),
          Text(
            item.label,
            style: theme.textTheme.bodySmall?.copyWith(
              color: isDark ? AppTheme.neutralGray400 : AppTheme.neutralGray600,
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildLongestSessionHighlight(UsageStatistics stats, bool isDark, ThemeData theme, Color primaryColor) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(UIConstants.spacingL.w),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            primaryColor.withOpacity(0.1),
            primaryColor.withOpacity(0.05),
          ],
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
        ),
        borderRadius: BorderRadius.circular(UIConstants.radiusM),
        border: Border.all(
          color: primaryColor.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(UIConstants.spacingM.w),
            decoration: BoxDecoration(
              color: primaryColor.withOpacity(0.2),
              borderRadius: BorderRadius.circular(UIConstants.radiusM),
            ),
            child: Icon(
              Icons.emoji_events,
              color: primaryColor,
              size: UIConstants.iconL,
            ),
          ),
          SizedBox(width: UIConstants.spacingL.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Longest Session',
                  style: theme.textTheme.titleMedium?.copyWith(
                    color: primaryColor,
                    fontWeight: FontWeight.w700,
                  ),
                ),
                SizedBox(height: UIConstants.spacingXS.h),
                Text(
                  TimeUtil.formatMinutes(stats.longestSession),
                  style: theme.textTheme.headlineSmall?.copyWith(
                    color: primaryColor,
                    fontWeight: FontWeight.w800,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(String message, bool isDark, ThemeData theme) {
    return SizedBox(
      height: 200.h,
      child: Container(
        decoration: BoxDecoration(
          color: isDark ? theme.cardBg : theme.colorScheme.surface,
          borderRadius: BorderRadius.circular(UIConstants.radiusM),
          border: Border.all(
            color: theme.colorScheme.error.withOpacity(0.3),
          ),
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: UIConstants.iconXL,
                color: theme.colorScheme.error,
              ),
              SizedBox(height: UIConstants.spacingM.h),
              Text(
                message,
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.error,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _StatItem {
  final String label;
  final String value;
  final IconData icon;
  final Color color;

  const _StatItem({
    required this.label,
    required this.value,
    required this.icon,
    required this.color,
  });
}
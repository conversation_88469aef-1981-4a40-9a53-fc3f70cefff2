import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../core/constants/ui_constants.dart';
import '../../../app/theme/app_theme.dart';
import 'app_detail_enums.dart';

class TimeRangeSelector extends StatelessWidget {
  final TimeRange selectedTimeRange;
  final ValueChanged<TimeRange> onChanged;

  const TimeRangeSelector({
    super.key,
    required this.selectedTimeRange,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Row(
      children: [
        Text(
          'Sessions for:',
          style: theme.textTheme.titleMedium?.copyWith(
            color: isDark ? AppTheme.neutralGray100 : AppTheme.neutralGray900,
            fontWeight: FontWeight.w600,
          ),
        ),
        const Spacer(),
        _buildDropdown(theme, isDark),
      ],
    );
  }

  Widget _buildDropdown(ThemeData theme, bool isDark) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: UIConstants.spacingM.w),
      decoration: BoxDecoration(
        color: isDark ? theme.cardBg : theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(UIConstants.radiusM),
        border: Border.all(
          color: isDark ? AppTheme.neutralGray600 : AppTheme.neutralGray400,
          width: 1,
        ),
      ),
      child: DropdownButton<TimeRange>(
        value: selectedTimeRange,
        underline: const SizedBox(),
        dropdownColor: isDark ? theme.cardBg : theme.colorScheme.surface,
        style: theme.textTheme.bodyMedium?.copyWith(
          color: isDark ? AppTheme.neutralGray100 : AppTheme.neutralGray900,
        ),
        items: TimeRange.values.map((range) => DropdownMenuItem<TimeRange>(
          value: range,
          child: Text(range.displayName),
        )).toList(),
        onChanged: (TimeRange? value) => value != null ? onChanged(value) : null,
      ),
    );
  }
}
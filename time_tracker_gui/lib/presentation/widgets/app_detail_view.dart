import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../data/models/app_model.dart';
import '../providers/app_providers.dart';
import '../providers/checkpoint_providers.dart';
import '../../core/constants/ui_constants.dart';
import '../../app/theme/app_theme.dart';

// Import the new components
import 'app_detail/app_detail_enums.dart';
import 'app_detail/app_hero_banner.dart';
import 'app_detail/app_stats_row.dart';
import 'app_detail/longest_session_card.dart';
import 'app_detail/time_range_selector.dart';
import 'app_detail/checkpoint_selector.dart';
import 'app_detail/checkpoint_stats_card.dart';
import 'app_detail/sessions_section.dart';
import 'app_detail/statistics_section.dart';
import 'checkpoint_dialog.dart';

class AppDetailView extends ConsumerStatefulWidget {
  final AppModel app;

  const AppDetailView({
    super.key,
    required this.app,
  });

  @override
  ConsumerState<AppDetailView> createState() => _AppDetailViewState();
}

class _AppDetailViewState extends ConsumerState<AppDetailView> {
  TimeRange _selectedTimeRange = TimeRange.lastSevenDays;
  CheckpointModel? _selectedCheckpoint;
  SessionSortOption _selectedSortOption = SessionSortOption.durationLongest;
  bool _showAllSessions = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadTimelineData();
    });
  }

  void _loadTimelineData() {
    final dateRange = _selectedTimeRange.getDateRange();
    ref.read(timelineProvider.notifier).loadTimeline(
      startDate: dateRange?.start,
      endDate: dateRange?.end,
      appId: widget.app.id,
    );
  }

  void _refreshCheckpointData() {
    invalidateCheckpointCache(ref, widget.app.id);

    ref.read(checkpointsForAppProvider(widget.app.id).notifier).refresh();
    ref.read(activeCheckpointForAppProvider(widget.app.id).notifier).refresh();
  }

  void _onTimeRangeChanged(TimeRange newRange) {
    setState(() {
      _selectedTimeRange = newRange;
      _showAllSessions = false;
    });
    _loadTimelineData();
  }

  void _onCheckpointChanged(CheckpointModel? checkpoint) {
    setState(() {
      _selectedCheckpoint = checkpoint;
      _showAllSessions = false;
    });
  }

  void _onSortOptionChanged(SessionSortOption newOption) {
    setState(() => _selectedSortOption = newOption);
  }

  void _onToggleShowAllSessions() {
    setState(() => _showAllSessions = !_showAllSessions);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Scaffold(
      body: CustomScrollView(
        slivers: [
          AppHeroBanner(
            app: widget.app,
            onEdit: () => _showEditDialog(context),
            onDelete: () => _showDeleteConfirmation(context),
          ),
          SliverToBoxAdapter(
            child: Container(
              color: isDark ? theme.darkBg : theme.colorScheme.surface,
              child: Center(
                child: ConstrainedBox(
                  constraints: BoxConstraints(maxWidth: UIConstants.maxContentWidth),
                  child: Padding(
                    padding: EdgeInsets.all(UIConstants.spacingL.w),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        AppStatsRow(app: widget.app),
                        SizedBox(height: UIConstants.spacingXL.h),

                        LongestSessionCard(app: widget.app),
                        SizedBox(height: UIConstants.spacingL.h),

                        _buildFiltersSection(theme, isDark),
                        SizedBox(height: UIConstants.spacingL.h),

                        if (_selectedCheckpoint != null) ...[
                          CheckpointStatsCard(
                            checkpoint: _selectedCheckpoint!,
                            appId: widget.app.id.toString(),
                          ),
                          SizedBox(height: UIConstants.spacingL.h),
                        ],

                        SessionsSection(
                          appId: widget.app.id.toString(),
                          selectedTimeRange: _selectedTimeRange,
                          selectedCheckpoint: _selectedCheckpoint,
                          selectedSortOption: _selectedSortOption,
                          showAllSessions: _showAllSessions,
                          onSortChanged: _onSortOptionChanged,
                          onToggleShowAll: _onToggleShowAllSessions,
                        ),
                        SizedBox(height: UIConstants.spacingL.h),

                        StatisticsSection(
                          appId: widget.app.id.toString(),
                          selectedTimeRange: _selectedTimeRange,
                          selectedCheckpoint: _selectedCheckpoint,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFiltersSection(ThemeData theme, bool isDark) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Filters',
          style: theme.textTheme.titleMedium?.copyWith(
            color: isDark ? AppTheme.neutralGray100 : AppTheme.neutralGray900,
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: UIConstants.spacingM.h),
        Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Time Range',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: isDark ? AppTheme.neutralGray400 : AppTheme.neutralGray600,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  SizedBox(height: UIConstants.spacingS.h),
                  TimeRangeSelector(
                    selectedTimeRange: _selectedTimeRange,
                    onChanged: _onTimeRangeChanged,
                  ),
                ],
              ),
            ),
            SizedBox(width: UIConstants.spacingL.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Checkpoint',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: isDark ? AppTheme.neutralGray400 : AppTheme.neutralGray600,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  SizedBox(height: UIConstants.spacingS.h),
                  CheckpointSelector(
                    app: widget.app,
                    selectedCheckpoint: _selectedCheckpoint,
                    onChanged: _onCheckpointChanged,
                  ),
                ],
              ),
            ),
          ],
        ),
        if (_selectedCheckpoint != null || _selectedTimeRange != TimeRange.allTime) ...[
          SizedBox(height: UIConstants.spacingM.h),
        ],
      ],
    );
  }



  void _showEditDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => CheckpointDialog(app: widget.app),
    );
  }

  void _showDeleteConfirmation(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: isDark ? theme.cardBg : theme.colorScheme.surface,
        title: Text(
          'Delete App',
          style: theme.textTheme.titleLarge?.copyWith(
            color: isDark ? AppTheme.neutralGray100 : AppTheme.neutralGray900,
          ),
        ),
        content: Text(
          'Are you sure you want to delete "${widget.app.name}"? This action cannot be undone.',
          style: theme.textTheme.bodyMedium?.copyWith(
            color: isDark ? AppTheme.neutralGray300 : AppTheme.neutralGray700,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              'Cancel',
              style: TextStyle(
                color: isDark ? AppTheme.neutralGray400 : AppTheme.neutralGray600,
              ),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              ref.read(appsProvider.notifier).deleteApp(widget.app.id);
              Navigator.of(context).pop();
              Navigator.of(context).pop();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: isDark ? AppTheme.accentColor : theme.colorScheme.error,
              foregroundColor: isDark ? AppTheme.neutralGray100 : Colors.white,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}

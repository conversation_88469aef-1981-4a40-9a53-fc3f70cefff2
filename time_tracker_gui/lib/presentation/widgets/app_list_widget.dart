import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'dart:ui';
import '../../data/models/app_model.dart';
import '../providers/app_providers.dart';
import '../../core/constants/ui_constants.dart';
import '../../app/theme/app_theme.dart';
import 'app_detail_view.dart';
import 'local_game_poster_widget.dart';

enum AppListViewMode { list, posters }

class AppListWidget extends ConsumerStatefulWidget {
  final List<AppModel> apps;
  final bool isCompact;
  final bool showViewToggle;

  const AppListWidget({
    super.key,
    required this.apps,
    this.isCompact = false,
    this.showViewToggle = true,
  });

  @override
  ConsumerState<AppListWidget> createState() => _AppListWidgetState();
}

class _AppListWidgetState extends ConsumerState<AppListWidget> {
  AppListViewMode _viewMode = AppListViewMode.list;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    final sessionCounts = ref.watch(sessionCountsProvider);

    if (widget.apps.isEmpty) {
      return Center(
        child: Container(
          padding: EdgeInsets.all(theme.spacingXXL.w),
          child: _buildEmptyLibraryCard(context, isDark, theme),
        ),
      );
    }

    return Column(
      mainAxisSize: widget.isCompact ? MainAxisSize.min : MainAxisSize.max,
      children: [
        // View toggle (only show if not compact and toggle is enabled)
        if (!widget.isCompact && widget.showViewToggle) ...[
          _buildViewToggle(theme, isDark),
          SizedBox(height: UIConstants.spacingM.h),
        ],
        
        // Content based on view mode
        if (widget.isCompact)
          _viewMode == AppListViewMode.list
              ? _buildListView(sessionCounts, isDark, theme, ref)
              : _buildPosterView(isDark, theme)
        else
          Expanded(
            child: _viewMode == AppListViewMode.list
                ? _buildListView(sessionCounts, isDark, theme, ref)
                : _buildPosterView(isDark, theme),
          ),
      ],
    );
  }

  Widget _buildViewToggle(ThemeData theme, bool isDark) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: UIConstants.spacingM.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          Container(
            decoration: BoxDecoration(
              color: isDark ? Colors.grey[800] : Colors.grey[100],
              borderRadius: BorderRadius.circular(UIConstants.radiusL),
              border: Border.all(
                color: isDark ? Colors.grey[700]! : Colors.grey[300]!,
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildToggleButton(
                  icon: Icons.list,
                  label: 'List',
                  isSelected: _viewMode == AppListViewMode.list,
                  onTap: () => setState(() => _viewMode = AppListViewMode.list),
                  theme: theme,
                  isDark: isDark,
                ),
                _buildToggleButton(
                  icon: Icons.grid_view,
                  label: 'Posters',
                  isSelected: _viewMode == AppListViewMode.posters,
                  onTap: () => setState(() => _viewMode = AppListViewMode.posters),
                  theme: theme,
                  isDark: isDark,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildToggleButton({
    required IconData icon,
    required String label,
    required bool isSelected,
    required VoidCallback onTap,
    required ThemeData theme,
    required bool isDark,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(UIConstants.radiusL),
        child: Container(
          padding: EdgeInsets.symmetric(
            horizontal: UIConstants.spacingM.w,
            vertical: UIConstants.spacingS.h,
          ),
          decoration: BoxDecoration(
            color: isSelected
                ? (isDark ? theme.colorScheme.primary.withOpacity(0.2) : theme.colorScheme.primary.withOpacity(0.1))
                : Colors.transparent,
            borderRadius: BorderRadius.circular(UIConstants.radiusL),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                size: 18.sp,
                color: isSelected
                    ? theme.colorScheme.primary
                    : (isDark ? Colors.grey[400] : Colors.grey[600]),
              ),
              SizedBox(width: UIConstants.spacingXS.w),
              Text(
                label,
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: isSelected
                      ? theme.colorScheme.primary
                      : (isDark ? Colors.grey[400] : Colors.grey[600]),
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildListView(AsyncValue<Map<int, int>> sessionCounts, bool isDark, ThemeData theme, WidgetRef ref) {
    return ListView.builder(
      shrinkWrap: widget.isCompact,
      physics: widget.isCompact ? const NeverScrollableScrollPhysics() : null,
      padding: EdgeInsets.symmetric(horizontal: theme.spacingM.w),
      itemCount: widget.apps.length,
      itemBuilder: (context, index) {
        final app = widget.apps[index];
        return Center(
          child: ConstrainedBox(
            constraints: BoxConstraints(maxWidth: UIConstants.maxContentWidth),
            child: _buildAppListItem(app, isDark, theme, ref, context, sessionCounts),
          ),
        );
      },
    );
  }

  Widget _buildPosterView(bool isDark, ThemeData theme) {
    return GridView.builder(
      padding: EdgeInsets.all(UIConstants.spacingM.w),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: _getPosterCrossAxisCount(),
        childAspectRatio: 0.7, // Poster aspect ratio
        crossAxisSpacing: UIConstants.spacingM.w,
        mainAxisSpacing: UIConstants.spacingM.h,
      ),
      itemCount: widget.apps.length,
      itemBuilder: (context, index) {
        final app = widget.apps[index];
        return LocalGamePosterWidget(
          app: app,
          onTap: () => _showAppDetails(context, app),
        );
      },
    );
  }

  int _getPosterCrossAxisCount() {
    final width = MediaQuery.of(context).size.width;
    if (width > UIConstants.tabletBreakpoint) {
      return 6; // Desktop: 6 columns
    } else if (width > UIConstants.mobileBreakpoint) {
      return 4; // Tablet: 4 columns
    } else {
      return 2; // Mobile: 2 columns
    }
  }

  int _getSessionCount(AppModel app, AsyncValue<Map<int, int>> sessionCounts) {
    return sessionCounts.when(
      data: (countsMap) => countsMap[app.id] ?? 0,
      loading: () => 0,
      error: (_, __) => 0,
    );
  }

  Widget _buildAppListItem(AppModel app, bool isDark, ThemeData theme, WidgetRef ref, BuildContext context, AsyncValue<Map<int, int>> sessionCounts) {
    final sessionCount = _getSessionCount(app, sessionCounts);

    return Container(
      margin: EdgeInsets.only(bottom: UIConstants.spacingXS, top: UIConstants.spacingXS),
      decoration: BoxDecoration(
        color: isDark ? theme.cardBg : theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(UIConstants.radiusL),
        border: Border.all(
          color: isDark ? AppTheme.neutralGray700 : AppTheme.neutralGray300,
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: isDark
              ? Colors.black.withOpacity(0.4)
              : Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _showAppDetails(context, app),
          borderRadius: BorderRadius.circular(UIConstants.radiusL),
          child: Padding(
            padding: EdgeInsets.all(UIConstants.spacingL.w),
            child: Row(
              children: [
                Container(
                  width: 6.w,
                  height: 40,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        isDark ? AppTheme.primaryColor : theme.colorScheme.primary,
                        (isDark ? AppTheme.primaryColor : theme.colorScheme.primary).withOpacity(0.5),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(3),
                  ),
                ),
                SizedBox(width: UIConstants.spacingL.w),
                Container(
                  padding: EdgeInsets.all(UIConstants.spacingM.w),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: isDark ? [
                        AppTheme.infoColor.withOpacity(0.3),
                        AppTheme.infoColor.withOpacity(0.1),
                      ] : [
                        AppTheme.infoColor.withOpacity(0.2),
                        AppTheme.infoColor.withOpacity(0.1),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(UIConstants.radiusM),
                  ),
                  child: Icon(
                    Icons.videogame_asset,
                    color: AppTheme.infoColor,
                    size: UIConstants.iconM,
                  ),
                ),
                SizedBox(width: UIConstants.spacingL.w),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        app.name ?? 'Unknown Game',
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w700,
                          color: isDark ? AppTheme.neutralGray100 : AppTheme.neutralGray900,
                        ),
                      ),
                      if (!widget.isCompact) ...[
                        SizedBox(height: UIConstants.spacingXS.h),
                        Text(
                          '$sessionCount sessions',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: isDark ? AppTheme.neutralGray400 : AppTheme.neutralGray600,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: UIConstants.spacingM,
                    vertical: UIConstants.spacingM,
                  ),
                  child: Text(
                    _formatDuration(app.duration ?? 0),
                    style: theme.textTheme.bodyLarge?.copyWith(
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                ),
                if (!widget.isCompact) ...[
                  SizedBox(width: UIConstants.spacingS.w),
                  Container(
                    width: 36,
                    height: 36,
                    decoration: BoxDecoration(
                      color: isDark
                        ? AppTheme.neutralGray700.withOpacity(0.8)
                        : AppTheme.neutralGray200.withOpacity(0.8),
                      shape: BoxShape.circle,
                    ),
                    child: IconButton(
                      icon: Icon(
                        Icons.delete,
                        size: 18,
                        color: isDark ? AppTheme.errorColor : AppTheme.errorColor.withOpacity(0.8),
                      ),
                      onPressed: () => _showDeleteConfirmation(context, ref, app),
                      tooltip: 'Remove from library',
                      padding: EdgeInsets.zero,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyLibraryCard(BuildContext context, bool isDark, ThemeData theme) {
    return Container(
      padding: EdgeInsets.all(theme.spacingXXL.w),
      decoration: BoxDecoration(
        color: isDark ? theme.cardBg : theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(theme.radiusXXL),
        border: Border.all(
          color: isDark
            ? theme.steamAccent.withOpacity(0.2)
            : theme.colorScheme.outline.withOpacity(0.3),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: isDark
              ? Colors.black.withOpacity(0.3)
              : Colors.black.withOpacity(0.08),
            blurRadius: 15,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: EdgeInsets.all(theme.spacingXL.w),
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: isDark
                ? theme.steamAccent.withOpacity(0.15)
                : theme.colorScheme.primaryContainer,
              border: Border.all(
                color: theme.steamAccent.withOpacity(0.3),
                width: 1.5,
              ),
            ),
            child: Icon(
              Icons.videogame_asset,
              size: UIConstants.iconXL * 1.8,
              color: theme.steamAccent,
            ),
          ),
          SizedBox(height: theme.spacingL.h),
          ShaderMask(
            shaderCallback: (bounds) => isDark
              ? theme.accentGradient.createShader(bounds)
              : LinearGradient(
                  colors: [theme.colorScheme.primary, theme.colorScheme.secondary],
                ).createShader(bounds),
            child: Text(
              'Your Game Library Awaits',
              style: theme.textTheme.displaySmall?.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.w800,
                letterSpacing: -0.5,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          SizedBox(height: theme.spacingM.h),
          Text(
            'Start your gaming session to see your library here.',
            style: theme.textTheme.bodyLarge?.copyWith(
              color: isDark
                ? Colors.white.withOpacity(0.7)
                : theme.colorScheme.onSurfaceVariant,
              height: 1.6,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: theme.spacingXL.h),
          Container(
            padding: EdgeInsets.symmetric(
              horizontal: theme.spacingL.w,
              vertical: theme.spacingM.h,
            ),
            decoration: BoxDecoration(
              color: isDark
                ? theme.steamAccent.withOpacity(0.1)
                : theme.colorScheme.primaryContainer,
              borderRadius: BorderRadius.circular(theme.radiusL),
              border: Border.all(
                color: theme.steamAccent.withOpacity(0.3),
                width: 1,
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.play_circle_outline,
                  color: theme.steamAccent,
                  size: UIConstants.iconM,
                ),
                SizedBox(width: theme.spacingS.w),
                Text(
                  'Ready to track your games',
                  style: theme.textTheme.labelLarge?.copyWith(
                    color: theme.steamAccent,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _showDeleteConfirmation(BuildContext context, WidgetRef ref, AppModel app) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: isDark ? theme.cardBg : theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(theme.radiusXL),
        ),
        title: Row(
          children: [
            Container(
              padding: EdgeInsets.all(theme.spacingS.w),
              decoration: BoxDecoration(
                color: theme.colorScheme.error.withOpacity(0.2),
                borderRadius: BorderRadius.circular(theme.radiusM),
              ),
              child: Icon(
                Icons.delete_outline,
                color: theme.colorScheme.error,
                size: UIConstants.iconM,
              ),
            ),
            SizedBox(width: theme.spacingM.w),
            Text(
              'Remove Game',
              style: theme.textTheme.titleLarge?.copyWith(
                color: isDark ? Colors.white : theme.colorScheme.onSurface,
                fontWeight: FontWeight.w700,
              ),
            ),
          ],
        ),
        content: Text(
          'Are you sure you want to remove "${app.name}" from your gaming library? This action cannot be undone.',
          style: theme.textTheme.bodyLarge?.copyWith(
            color: isDark
              ? Colors.white.withOpacity(0.8)
              : theme.colorScheme.onSurfaceVariant,
            height: 1.5,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            style: TextButton.styleFrom(
              foregroundColor: isDark
                ? Colors.white.withOpacity(0.7)
                : theme.colorScheme.onSurfaceVariant,
            ),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              ref.read(appsProvider.notifier).deleteApp(app.id);
              Navigator.of(context).pop();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: theme.colorScheme.error,
              foregroundColor: Colors.white,
              elevation: theme.elevationLow,
            ),
            child: const Text('Remove'),
          ),
        ],
      ),
    );
  }

  String _formatDuration(int minutes) {
    final hours = minutes ~/ 60;
    final mins = minutes % 60;

    if (hours > 0) {
      return '${hours}h ${mins}m';
    } else if (minutes > 0) {
      return '${minutes}m';
    } else {
      return '${mins}m';
    }
  }

  void _showAppDetails(BuildContext context, AppModel app) {
    Navigator.of(context).push(
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) =>
            AppDetailView(app: app),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          const begin = Offset(1.0, 0.0);
          const end = Offset.zero;
          const curve = Curves.easeInOutCubic;

          var tween = Tween(begin: begin, end: end).chain(
            CurveTween(curve: curve),
          );

          return SlideTransition(
            position: animation.drive(tween),
            child: child,
          );
        },
        transitionDuration: const Duration(milliseconds: 300),
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'dart:ui';
import '../providers/app_providers.dart';
import '../../core/constants/ui_constants.dart';
import '../../app/theme/app_theme.dart';
import 'app_list_widget.dart';
import 'common_ui_components.dart';
import 'dialog_components.dart';

class AppsPageComponents {
  static Widget buildGamingAppsHeader(bool isDark, ThemeData theme, WidgetRef ref) {
    final searchState = ref.watch(appSearchProvider);
    final hasActiveFilters = searchState.searchQuery.isNotEmpty || 
                           searchState.sortBy != AppSortBy.name || 
                           searchState.sortOrder != SortOrder.ascending;
    
    return CommonUIComponents.buildGlassyFilterContainer(
      isDark: isDark,
      theme: theme,
      child: Center(
        child: ConstrainedBox(
          constraints: BoxConstraints(maxWidth: UIConstants.maxContentWidth),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: CommonUIComponents.buildGlassySearchBar(
                      hintText: 'Search your gaming library...',
                      currentValue: searchState.searchQuery,
                      onChanged: (value) => ref.read(appSearchProvider.notifier).updateSearchQuery(value),
                      onClear: () => ref.read(appSearchProvider.notifier).clearSearch(),
                      isDark: isDark,
                      theme: theme,
                    ),
                  ),
                  SizedBox(width: UIConstants.spacingM),
                  Stack(
                    children: [
                      CommonUIComponents.buildGlassyFilterButton(
                        () => DialogComponents.showFilterDialog(ref.context, ref),
                        isDark,
                        theme,
                      ),
                      if (hasActiveFilters)
                        Positioned(
                          right: 8,
                          top: 8,
                          child: Container(
                            width: 8,
                            height: 8,
                            decoration: BoxDecoration(
                              color: isDark ? AppTheme.primaryColor : theme.colorScheme.primary,
                              shape: BoxShape.circle,
                            ),
                          ),
                        ),
                    ],
                  ),
                  if (hasActiveFilters) ...[
                    SizedBox(width: UIConstants.spacingS),
                    CommonUIComponents.buildClearFiltersButton(
                      onPressed: () => ref.read(appSearchProvider.notifier).clearAllFilters(),
                      isDark: isDark,
                      tooltip: 'Clear all filters',
                    ),
                  ],
                ],
              ),
              if (hasActiveFilters) ...[
                SizedBox(height: UIConstants.spacingS),
                Wrap(
                  spacing: UIConstants.spacingS,
                  children: [
                    if (searchState.searchQuery.isNotEmpty)
                      buildFilterChip(
                        'Search: "${searchState.searchQuery}"',
                        () => ref.read(appSearchProvider.notifier).clearSearch(),
                        isDark,
                        theme,
                      ),
                    if (searchState.sortBy != AppSortBy.name)
                      buildFilterChip(
                        'Sort: ${_getSortByLabel(searchState.sortBy)}',
                        () => ref.read(appSearchProvider.notifier).updateSortBy(AppSortBy.name),
                        isDark,
                        theme,
                      ),
                    if (searchState.sortOrder != SortOrder.ascending)
                      buildFilterChip(
                        'Order: ${_getSortOrderLabel(searchState.sortOrder)}',
                        () => ref.read(appSearchProvider.notifier).updateSortOrder(SortOrder.ascending),
                        isDark,
                        theme,
                      ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  static Widget buildFilterChip(String label, VoidCallback onRemove, bool isDark, ThemeData theme) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: UIConstants.spacingS,
        vertical: UIConstants.spacingXS,
      ),
      decoration: BoxDecoration(
        color: isDark 
          ? AppTheme.primaryColor.withOpacity(0.2)
          : theme.colorScheme.primary.withOpacity(0.1),
        borderRadius: BorderRadius.circular(UIConstants.radiusS),
        border: Border.all(
          color: isDark ? AppTheme.primaryColor : theme.colorScheme.primary,
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            label,
            style: TextStyle(
              color: isDark ? AppTheme.primaryColor : theme.colorScheme.primary,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(width: UIConstants.spacingXS),
          GestureDetector(
            onTap: onRemove,
            child: Icon(
              Icons.close,
              size: 14,
              color: isDark ? AppTheme.primaryColor : theme.colorScheme.primary,
            ),
          ),
        ],
      ),
    );
  }

  static String _getSortByLabel(AppSortBy sortBy) {
    switch (sortBy) {
      case AppSortBy.name:
        return 'Name';
      case AppSortBy.duration:
        return 'Total Time';
      case AppSortBy.launches:
        return 'Times Played';
      case AppSortBy.lastUsed:
        return 'Last Played';
    }
  }

  static String _getSortOrderLabel(SortOrder order) {
    switch (order) {
      case SortOrder.ascending:
        return 'Ascending';
      case SortOrder.descending:
        return 'Descending';
    }
  }

  static Widget buildEmptyGamesLibrary(bool isDark, ThemeData theme, WidgetRef ref) {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(UIConstants.spacingXXL.w),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(UIConstants.radiusXXL),
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 15, sigmaY: 15),
            child: Container(
              padding: EdgeInsets.all(UIConstants.spacingXXL.w),
              decoration: BoxDecoration(
                color: isDark
                  ? Colors.white.withOpacity(0.05)
                  : Colors.black.withOpacity(0.02),
                borderRadius: BorderRadius.circular(UIConstants.radiusXXL),
                border: Border.all(
                  color: isDark
                    ? Colors.white.withOpacity(0.1)
                    : Colors.black.withOpacity(0.05),
                  width: 1,
                ),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    padding: EdgeInsets.all(UIConstants.spacingXXL.w),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: isDark ? [
                          AppTheme.primaryColor.withOpacity(0.3),
                          AppTheme.accentColor.withOpacity(0.2),
                        ] : [
                          theme.colorScheme.primary.withOpacity(0.2),
                          theme.colorScheme.secondary.withOpacity(0.1),
                        ],
                      ),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.games,
                      size: UIConstants.iconXL * 2,
                      color: isDark ? AppTheme.primaryColor : theme.colorScheme.primary,
                    ),
                  ),
                  SizedBox(height: UIConstants.spacingXL.h),
                  Text(
                    'No Games in Library',
                    style: theme.textTheme.headlineMedium?.copyWith(
                      fontWeight: FontWeight.w700,
                      color: isDark ? AppTheme.neutralGray100 : AppTheme.neutralGray900,
                    ),
                  ),
                  SizedBox(height: UIConstants.spacingL.h),
                  Text(
                    'Start building your gaming library to track sessions\nand discover new adventures!',
                    style: theme.textTheme.bodyLarge?.copyWith(
                      color: isDark ? AppTheme.neutralGray400 : AppTheme.neutralGray600,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(height: UIConstants.spacingXXL.h),
                  CommonUIComponents.buildGlassyActionButton(
                    'Add Your First Game',
                    Icons.add,
                    () => DialogComponents.showAddAppDialog(ref.context, ref),
                    isDark,
                    theme,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
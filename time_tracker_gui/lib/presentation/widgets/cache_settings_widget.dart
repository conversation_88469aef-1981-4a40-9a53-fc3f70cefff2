import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../core/constants/ui_constants.dart';
import '../providers/local_game_poster_providers.dart';

class CacheSettingsWidget extends ConsumerWidget {
  const CacheSettingsWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    final cacheStatsAsyncValue = ref.watch(cacheStatsProvider);

    return Card(
      margin: EdgeInsets.all(UIConstants.spacingM.w),
      child: Padding(
        padding: EdgeInsets.all(UIConstants.spacingL.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Icon(
                  Icons.storage,
                  color: theme.colorScheme.primary,
                  size: 24.sp,
                ),
                SizedBox(width: UIConstants.spacingM.w),
                Text(
                  'Poster Cache',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),

            SizedBox(height: UIConstants.spacingL.h),

            // Description
            Text(
              'Game posters are cached locally to reduce API usage and improve loading times. Cached images are automatically refreshed after 7 days.',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: isDark ? Colors.grey[300] : Colors.grey[700],
              ),
            ),

            SizedBox(height: UIConstants.spacingL.h),

            // Cache Statistics
            _buildCacheStats(context, ref, cacheStatsAsyncValue, isDark, theme),

            SizedBox(height: UIConstants.spacingL.h),

            // Actions
            _buildCacheActions(context, ref, theme),
          ],
        ),
      ),
    );
  }

  Widget _buildCacheStats(
    BuildContext context,
    WidgetRef ref,
    AsyncValue<Map<String, dynamic>> cacheStatsAsyncValue,
    bool isDark,
    ThemeData theme,
  ) {
    return Container(
      padding: EdgeInsets.all(UIConstants.spacingM.w),
      decoration: BoxDecoration(
        color: isDark ? Colors.grey[800] : Colors.grey[100],
        borderRadius: BorderRadius.circular(UIConstants.radiusL),
        border: Border.all(
          color: isDark ? Colors.grey[700]! : Colors.grey[300]!,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Cache Statistics',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: UIConstants.spacingM.h),
          cacheStatsAsyncValue.when(
            data: (stats) => Column(
              children: [
                _buildStatRow(
                  'Cache Size',
                  stats['sizeFormatted'] as String,
                  Icons.folder_outlined,
                  theme,
                ),
                SizedBox(height: UIConstants.spacingS.h),
                _buildStatRow(
                  'Storage Location',
                  'Local Documents',
                  Icons.location_on_outlined,
                  theme,
                ),
                SizedBox(height: UIConstants.spacingS.h),
                _buildStatRow(
                  'Cache Expiry',
                  '7 days',
                  Icons.schedule_outlined,
                  theme,
                ),
              ],
            ),
            loading: () => const Center(child: CircularProgressIndicator()),
            error: (error, stackTrace) => Text(
              'Failed to load cache statistics',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: Colors.red,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatRow(String label, String value, IconData icon, ThemeData theme) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16.sp,
          color: theme.colorScheme.primary,
        ),
        SizedBox(width: UIConstants.spacingS.w),
        Text(
          label,
          style: theme.textTheme.bodyMedium,
        ),
        const Spacer(),
        Text(
          value,
          style: theme.textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  Widget _buildCacheActions(BuildContext context, WidgetRef ref, ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Text(
          'Cache Management',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: UIConstants.spacingM.h),

        // Refresh Cache Button
        ElevatedButton.icon(
          onPressed: () => _refreshCache(context, ref),
          icon: const Icon(Icons.refresh),
          label: const Text('Refresh Cache'),
          style: ElevatedButton.styleFrom(
            padding: EdgeInsets.symmetric(
              horizontal: UIConstants.spacingL.w,
              vertical: UIConstants.spacingM.h,
            ),
          ),
        ),

        SizedBox(height: UIConstants.spacingM.h),

        // Clear Cache Button
        OutlinedButton.icon(
          onPressed: () => _showClearCacheDialog(context, ref),
          icon: const Icon(Icons.delete_outline),
          label: const Text('Clear Cache'),
          style: OutlinedButton.styleFrom(
            foregroundColor: Colors.red,
            side: const BorderSide(color: Colors.red),
            padding: EdgeInsets.symmetric(
              horizontal: UIConstants.spacingL.w,
              vertical: UIConstants.spacingM.h,
            ),
          ),
        ),
      ],
    );
  }

  void _refreshCache(BuildContext context, WidgetRef ref) {
    // Invalidate cache providers to force refresh
    ref.invalidate(cacheStatsProvider);
    ref.invalidate(cachedPosterProvider);
    ref.invalidate(cachedImagePathProvider);

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Cache refreshed successfully'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _showClearCacheDialog(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear Cache'),
        content: const Text(
          'This will remove all cached game posters and images. They will be re-downloaded when needed. Are you sure?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _clearCache(context, ref);
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Clear'),
          ),
        ],
      ),
    );
  }

  void _clearCache(BuildContext context, WidgetRef ref) async {
    try {
      // Clear the cache
      await ref.read(clearCacheProvider.future);

      // Invalidate related providers
      ref.invalidate(cachedPosterProvider);
      ref.invalidate(cachedImagePathProvider);
      ref.invalidate(localGamePostersProvider);

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Cache cleared successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to clear cache: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'dart:ui';
import '../../core/constants/ui_constants.dart';
import '../../app/theme/app_theme.dart';

class CommonUIComponents {
  static Widget buildErrorState(String message) {
    return Builder(
      builder: (context) {
        final theme = Theme.of(context);
        final isDark = theme.brightness == Brightness.dark;

        return Center(
          child: Padding(
            padding: EdgeInsets.all(UIConstants.spacingL.w),
            child: Column(
              children: [
                SizedBox(height: UIConstants.spacingS.h),
                Icon(
                  Icons.error_outline,
                  size: UIConstants.iconL,
                  color: AppTheme.errorColor,
                ),
                SizedBox(height: UIConstants.spacingM.h),
                Text(
                  message,
                  style: theme.textTheme.bodyLarge?.copyWith(
                    color: AppTheme.errorColor,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  static Widget buildGamingEmptyState(String title, String subtitle, bool isDark, ThemeData theme) {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(UIConstants.spacingXXL.w),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(UIConstants.radiusXXL),
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 15, sigmaY: 15),
            child: Container(
              padding: EdgeInsets.all(UIConstants.spacingXXL.w),
              decoration: BoxDecoration(
                color: isDark
                  ? Colors.black.withOpacity(0.3)
                  : theme.colorScheme.surface.withOpacity(0.8),
                borderRadius: BorderRadius.circular(UIConstants.radiusXXL),
                border: Border.all(
                  color: isDark
                    ? Colors.white.withOpacity(0.1)
                    : AppTheme.neutralGray300.withOpacity(0.5),
                  width: 1,
                ),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    padding: EdgeInsets.all(UIConstants.spacingXXL.w),
                    decoration: BoxDecoration(
                      color: Colors.transparent,
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: (isDark ? AppTheme.primaryColor : theme.colorScheme.primary).withOpacity(0.5),
                        width: 2,
                      ),
                    ),
                    child: Icon(
                      Icons.videogame_asset_off,
                      size: UIConstants.iconXL * 2,
                      color: isDark ? AppTheme.primaryColor : theme.colorScheme.primary,
                    ),
                  ),
                  SizedBox(height: UIConstants.spacingXL.h),
                  Text(
                    title,
                    style: theme.textTheme.titleLarge?.copyWith(
                      color: isDark ? Colors.white : theme.colorScheme.onSurfaceVariant,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  SizedBox(height: UIConstants.spacingM.h),
                  Text(
                    subtitle,
                    style: theme.textTheme.bodyLarge?.copyWith(
                      color: isDark ? Colors.white.withOpacity(0.6) : theme.colorScheme.onSurfaceVariant,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  static Widget buildGlassyActionButton(String text, IconData icon, VoidCallback onPressed, bool isDark, ThemeData theme) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(UIConstants.radiusL),
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
        child: Container(
          decoration: BoxDecoration(
            color: isDark
              ? Colors.black.withOpacity(0.3)
              : theme.colorScheme.surface.withOpacity(0.8),
            borderRadius: BorderRadius.circular(UIConstants.radiusL),
            border: Border.all(
              color: (isDark ? AppTheme.primaryColor : theme.colorScheme.primary).withOpacity(0.5),
              width: 1,
            ),
          ),
          child: ElevatedButton.icon(
            onPressed: onPressed,
            icon: Icon(icon),
            label: Text(text),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.transparent,
              foregroundColor: isDark ? AppTheme.primaryColor : theme.colorScheme.primary,
              elevation: 0,
              padding: EdgeInsets.symmetric(
                horizontal: UIConstants.spacingL.w,
                vertical: UIConstants.spacingM.h,
              ),
            ),
          ),
        ),
      ),
    );
  }

  static Widget buildGlassyFilterButton(VoidCallback onPressed, bool isDark, ThemeData theme) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(UIConstants.radiusM),
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
        child: Container(
          decoration: BoxDecoration(
            color: isDark
              ? Colors.black.withOpacity(0.3)
              : theme.colorScheme.surface.withOpacity(0.8),
            borderRadius: BorderRadius.circular(UIConstants.radiusM),
            border: Border.all(
              color: isDark
                ? Colors.white.withOpacity(0.2)
                : AppTheme.neutralGray400.withOpacity(0.5),
              width: 1,
            ),
          ),
          child: IconButton(
            icon: Icon(
              Icons.filter_list,
              color: isDark ? Colors.white : AppTheme.neutralGray900,
            ),
            onPressed: onPressed,
          ),
        ),
      ),
    );
  }

  /// Shared glassy search bar component used across the app
  static Widget buildGlassySearchBar({
    required String hintText,
    required String currentValue,
    required ValueChanged<String> onChanged,
    required VoidCallback? onClear,
    required bool isDark,
    required ThemeData theme,
    Widget? suffixIcon,
  }) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(UIConstants.radiusL),
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
        child: Container(
          decoration: BoxDecoration(
            color: isDark
              ? Colors.white.withOpacity(0.1)
              : Colors.black.withOpacity(0.05),
            borderRadius: BorderRadius.circular(UIConstants.radiusL),
            border: Border.all(
              color: isDark
                ? Colors.white.withOpacity(0.2)
                : Colors.black.withOpacity(0.1),
              width: 1,
            ),
          ),
          child: TextField(
            onChanged: onChanged,
            decoration: InputDecoration(
              hintText: hintText,
              hintStyle: TextStyle(
                color: isDark ? AppTheme.neutralGray400 : AppTheme.neutralGray600,
              ),
              prefixIcon: Icon(
                Icons.search,
                color: isDark ? AppTheme.primaryColor : theme.colorScheme.primary,
              ),
              suffixIcon: currentValue.isNotEmpty
                  ? IconButton(
                      icon: Icon(
                        Icons.clear,
                        color: isDark ? AppTheme.neutralGray400 : AppTheme.neutralGray600,
                      ),
                      onPressed: onClear,
                    )
                  : suffixIcon,
              border: InputBorder.none,
              contentPadding: EdgeInsets.symmetric(
                horizontal: UIConstants.spacingM,
                vertical: UIConstants.spacingM,
              ),
            ),
            style: TextStyle(
              color: isDark ? AppTheme.neutralGray100 : AppTheme.neutralGray900,
            ),
          ),
        ),
      ),
    );
  }

  /// Shared glassy container for filter sections
  static Widget buildGlassyFilterContainer({
    required Widget child,
    required bool isDark,
    required ThemeData theme,
    EdgeInsets? padding,
  }) {
    return ClipRRect(
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
        child: Container(
          padding: padding ?? EdgeInsets.all(UIConstants.spacingL),
          decoration: BoxDecoration(
            color: isDark
              ? Colors.white.withOpacity(0.05)
              : Colors.black.withOpacity(0.02),
            border: Border(
              bottom: BorderSide(
                color: isDark ? AppTheme.neutralGray700 : AppTheme.neutralGray300,
                width: 1,
              ),
            ),
          ),
          child: child,
        ),
      ),
    );
  }

  /// Shared dropdown field with consistent styling
  static Widget buildStyledDropdown<T>({
    required T? value,
    required List<DropdownMenuItem<T>> items,
    required ValueChanged<T?> onChanged,
    required String labelText,
    required bool isDark,
    required ThemeData theme,
  }) {
    return DropdownButtonFormField<T>(
      value: value,
      decoration: InputDecoration(
        labelText: labelText,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(UIConstants.radiusM),
        ),
        filled: true,
        fillColor: isDark ? AppTheme.neutralGray800 : AppTheme.neutralGray100,
      ),
      dropdownColor: isDark ? AppTheme.neutralGray800 : Colors.white,
      style: TextStyle(
        color: isDark ? AppTheme.neutralGray100 : AppTheme.neutralGray900,
      ),
      items: items,
      onChanged: onChanged,
    );
  }

  /// Shared clear filters button
  static Widget buildClearFiltersButton({
    required VoidCallback onPressed,
    required bool isDark,
    String tooltip = 'Clear all filters',
  }) {
    return IconButton(
      onPressed: onPressed,
      icon: Icon(
        Icons.clear_all,
        color: isDark ? AppTheme.neutralGray400 : AppTheme.neutralGray600,
      ),
      tooltip: tooltip,
    );
  }
}
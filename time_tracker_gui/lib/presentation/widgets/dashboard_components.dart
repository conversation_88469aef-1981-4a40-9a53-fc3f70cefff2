import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'dart:ui';
import '../providers/app_providers.dart';
import '../providers/poster_providers.dart';
import '../providers/daily_session_provider.dart';
import '../../core/constants/ui_constants.dart';
import '../../app/theme/app_theme.dart';
import '../../data/models/app_model.dart';
import '../../data/models/poster_model.dart';
import '../../util/time_util.dart';
import 'tracking_controls.dart';
import 'app_list_widget.dart';
import 'common_ui_components.dart';

class DashboardComponents {
  static Widget buildFrostedTrackingCard(AsyncValue trackingStatus, bool isDark, ThemeData theme) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(UIConstants.radiusXL),
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 15, sigmaY: 15),
        child: Container(
          padding: EdgeInsets.all(UIConstants.spacingXL.w),
          decoration: BoxDecoration(
            color: isDark
              ? Colors.black.withOpacity(0.3)
              : theme.colorScheme.surface.withOpacity(0.8),
            borderRadius: BorderRadius.circular(UIConstants.radiusXL),
            border: Border.all(
              color: isDark
                ? Colors.white.withOpacity(0.2)
                : AppTheme.neutralGray300.withOpacity(0.5),
              width: 1,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: EdgeInsets.all(UIConstants.spacingM.w),
                    decoration: BoxDecoration(
                      color: Colors.transparent,
                      borderRadius: BorderRadius.circular(UIConstants.radiusM),
                      border: Border.all(
                        color: AppTheme.successColor.withOpacity(0.5),
                        width: 2,
                      ),
                    ),
                    child: Icon(
                      Icons.track_changes,
                      color: AppTheme.successColor,
                      size: UIConstants.iconM,
                    ),
                  ),
                  SizedBox(width: UIConstants.spacingM.w),
                  Text(
                    'Session Tracking',
                    style: theme.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.w700,
                      color: isDark ? Colors.white : AppTheme.neutralGray900,
                    ),
                  ),
                ],
              ),
              SizedBox(height: UIConstants.spacingL.h),
              trackingStatus.when(
                data: (status) => TrackingControls(status: status),
                loading: () => const Center(child: CircularProgressIndicator()),
                error: (error, _) => CommonUIComponents.buildErrorState('Failed to load tracking status'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  static Widget buildEnhancedStatsGrid(AsyncValue trackingStatus, AsyncValue apps, bool isDark, ThemeData theme) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Use a slightly lower breakpoint for internal layout to ensure consistency
        final isWide = constraints.maxWidth > (UIConstants.tabletBreakpoint - UIConstants.breakpointHysteresis);

        if (isWide) {
          return Row(
            children: [
              Expanded(child: _buildEnhancedTotalGamesCard(apps, isDark, theme)),
              SizedBox(width: UIConstants.spacingL.w),
              Expanded(child: _buildEnhancedTodayTimeCard(isDark, theme)),
            ],
          );
        } else {
          return Column(
            children: [
              Row(
                children: [
                  Expanded(child: _buildEnhancedTotalGamesCard(apps, isDark, theme)),
                  SizedBox(width: UIConstants.spacingL.w),
                  Expanded(child: _buildEnhancedTodayTimeCard(isDark, theme)),
                ],
              ),
            ],
          );
        }
      },
    );
  }

  static Widget _buildEnhancedTotalGamesCard(AsyncValue apps, bool isDark, ThemeData theme) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(UIConstants.radiusXL),
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 15, sigmaY: 15),
        child: Container(
          padding: EdgeInsets.all(UIConstants.spacingXL.w),
          decoration: BoxDecoration(
            color: isDark
              ? Colors.black.withOpacity(0.3)
              : theme.colorScheme.surface.withOpacity(0.8),
            borderRadius: BorderRadius.circular(UIConstants.radiusXL),
            border: Border.all(
              color: isDark
                ? Colors.white.withOpacity(0.2)
                : AppTheme.neutralGray300.withOpacity(0.5),
              width: 1,
            ),
          ),
          child: apps.when(
            data: (appList) => _buildEnhancedStatDisplay(
              '${appList.length}',
              'games in library',
              Icons.library_books,
              isDark ? AppTheme.primaryColor : theme.colorScheme.primary,
              isDark,
              theme,
            ),
            loading: () => const Center(child: CircularProgressIndicator()),
            error: (_, __) => CommonUIComponents.buildErrorState('Error loading games'),
          ),
        ),
      ),
    );
  }

  static Widget _buildEnhancedTodayTimeCard(bool isDark, ThemeData theme) {
    return Consumer(
      builder: (context, ref, child) {
        final todaySession = ref.watch(todaySessionProvider);

        return ClipRRect(
          borderRadius: BorderRadius.circular(UIConstants.radiusXL),
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 15, sigmaY: 15),
            child: Container(
              padding: EdgeInsets.all(UIConstants.spacingXL.w),
              decoration: BoxDecoration(
                color: isDark
                  ? Colors.black.withOpacity(0.3)
                  : theme.colorScheme.surface.withOpacity(0.8),
                borderRadius: BorderRadius.circular(UIConstants.radiusXL),
                border: Border.all(
                  color: isDark
                    ? Colors.white.withOpacity(0.2)
                    : AppTheme.neutralGray300.withOpacity(0.5),
                  width: 1,
                ),
              ),
              child: todaySession.when(
                data: (data) => _buildEnhancedStatDisplay(
                  TimeUtil.formatDurationFromMinutes(data.totalMinutes),
                  'playtime today',
                  Icons.schedule,
                  AppTheme.successColor,
                  isDark,
                  theme,
                ),
                loading: () => const Center(child: CircularProgressIndicator()),
                error: (_, __) => _buildEnhancedStatDisplay(
                  '0m',
                  'playtime today',
                  Icons.schedule,
                  AppTheme.successColor,
                  isDark,
                  theme,
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  static Widget _buildEnhancedStatDisplay(String value, String subtitle, IconData icon, Color color, bool isDark, ThemeData theme) {
    return Row(
      children: [
        Container(
          padding: EdgeInsets.all(UIConstants.spacingL.w),
          decoration: BoxDecoration(
            color: Colors.transparent,
            borderRadius: BorderRadius.circular(UIConstants.radiusL),
            border: Border.all(
              color: color.withOpacity(0.5),
              width: 2,
            ),
          ),
          child: Icon(
            icon,
            color: color,
            size: UIConstants.iconL,
          ),
        ),
        SizedBox(width: UIConstants.spacingL.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                value,
                style: theme.textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.w700,
                  color: color,
                ),
              ),
              Text(
                subtitle,
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: isDark ? Colors.white.withOpacity(0.6) : AppTheme.neutralGray600,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  static Widget buildGamingAppsSection(AsyncValue apps, bool isDark, ThemeData theme, VoidCallback onViewAll) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(UIConstants.radiusXL),
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 15, sigmaY: 15),
        child: Container(
          padding: EdgeInsets.all(UIConstants.spacingXL.w),
          decoration: BoxDecoration(
            color: isDark
              ? Colors.black.withOpacity(0.3)
              : theme.colorScheme.surface.withOpacity(0.8),
            borderRadius: BorderRadius.circular(UIConstants.radiusXL),
            border: Border.all(
              color: isDark
                ? Colors.white.withOpacity(0.2)
                : AppTheme.neutralGray300.withOpacity(0.5),
              width: 1,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: EdgeInsets.all(UIConstants.spacingM.w),
                    decoration: BoxDecoration(
                      color: Colors.transparent,
                      borderRadius: BorderRadius.circular(UIConstants.radiusM),
                      border: Border.all(
                        color: (isDark ? AppTheme.accentColor : theme.colorScheme.secondary).withOpacity(0.5),
                        width: 2,
                      ),
                    ),
                    child: Icon(
                      Icons.games,
                      color: isDark ? AppTheme.accentColor : theme.colorScheme.secondary,
                      size: UIConstants.iconM,
                    ),
                  ),
                  SizedBox(width: UIConstants.spacingM.w),
                  Text(
                    'Recent Games',
                    style: theme.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.w700,
                      color: isDark ? Colors.white : AppTheme.neutralGray900,
                    ),
                  ),
                  const Spacer(),
                  TextButton(
                    onPressed: onViewAll,
                    style: TextButton.styleFrom(
                      foregroundColor: isDark ? AppTheme.primaryColor : theme.colorScheme.primary,
                    ),
                    child: const Text('View All'),
                  ),
                ],
              ),
              SizedBox(height: UIConstants.spacingL.h),
              apps.when(
                data: (appList) => appList.isEmpty
                    ? CommonUIComponents.buildGamingEmptyState('No games tracked yet', 'Start playing to see your library here', isDark, theme)
                    : AppListWidget(
                        apps: appList.take(5).toList(),
                        isCompact: true,
                      ),
                loading: () => const Center(child: CircularProgressIndicator()),
                error: (error, _) => CommonUIComponents.buildErrorState('Failed to load games'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  static Widget buildFeaturedPostersSection(AsyncValue postersAsyncValue, bool isDark, ThemeData theme) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(UIConstants.radiusXL),
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
        child: Container(
          padding: EdgeInsets.all(UIConstants.spacingXL.w),
          decoration: BoxDecoration(
            color: isDark
              ? Colors.white.withOpacity(0.05)
              : Colors.black.withOpacity(0.02),
            borderRadius: BorderRadius.circular(UIConstants.radiusXL),
            border: Border.all(
              color: isDark
                ? Colors.white.withOpacity(0.2)
                : Colors.black.withOpacity(0.1),
              width: 1,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: EdgeInsets.all(UIConstants.spacingM.w),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: isDark ? [
                          AppTheme.infoColor.withOpacity(0.3),
                          AppTheme.infoColor.withOpacity(0.1),
                        ] : [
                          AppTheme.infoColor.withOpacity(0.2),
                          AppTheme.infoColor.withOpacity(0.1),
                        ],
                      ),
                      borderRadius: BorderRadius.circular(UIConstants.radiusM),
                    ),
                    child: Icon(
                      Icons.featured_play_list,
                      color: AppTheme.infoColor,
                      size: UIConstants.iconM,
                    ),
                  ),
                  SizedBox(width: UIConstants.spacingM.w),
                  Text(
                    'Featured Content',
                    style: theme.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.w700,
                      color: isDark ? AppTheme.neutralGray100 : AppTheme.neutralGray900,
                    ),
                  ),
                ],
              ),
              SizedBox(height: UIConstants.spacingL.h),
              postersAsyncValue.when(
                data: (posters) {
                  if (posters.isEmpty) {
                    return CommonUIComponents.buildGamingEmptyState('No featured content available', 'Check back later for gaming highlights', isDark, theme);
                  } else {
                    return SizedBox(
                      height: 250.h,
                      child: ListView.builder(
                        scrollDirection: Axis.horizontal,
                        itemCount: posters.length,
                        itemBuilder: (context, index) {
                          final poster = posters[index];
                          return Padding(
                            padding: EdgeInsets.only(right: UIConstants.spacingL.w),
                            child: _buildGamingPosterItem(poster, isDark, theme),
                          );
                        },
                      ),
                    );
                  }
                },
                loading: () => const Center(child: CircularProgressIndicator()),
                error: (error, _) => CommonUIComponents.buildErrorState('Failed to load featured content'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  static Widget _buildGamingPosterItem(Poster poster, bool isDark, ThemeData theme) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(UIConstants.radiusL),
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
        child: Container(
          width: 200.w,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: isDark ? [
                theme.cardBg.withOpacity(0.8),
                theme.cardBg.withOpacity(0.6),
              ] : [
                theme.colorScheme.surface.withOpacity(0.8),
                theme.colorScheme.surfaceContainerHighest.withOpacity(0.6),
              ],
            ),
            borderRadius: BorderRadius.circular(UIConstants.radiusL),
            border: Border.all(
              color: isDark ? AppTheme.neutralGray500 : AppTheme.neutralGray300,
              width: 1,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Expanded(
                child: ClipRRect(
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(UIConstants.radiusL),
                    topRight: Radius.circular(UIConstants.radiusL),
                  ),
                  child: Image.network(
                    poster.backgroundImage,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) => Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: isDark ? [
                            AppTheme.neutralGray700,
                            AppTheme.neutralGray800,
                          ] : [
                            AppTheme.neutralGray300,
                            AppTheme.neutralGray400,
                          ],
                        ),
                      ),
                      child: Center(
                        child: Icon(
                          Icons.image_not_supported,
                          color: isDark ? AppTheme.neutralGray500 : AppTheme.neutralGray600,
                          size: UIConstants.iconL,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
              Padding(
                padding: EdgeInsets.all(UIConstants.spacingM.w),
                child: Text(
                  poster.name,
                  textAlign: TextAlign.center,
                  style: theme.textTheme.bodyLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: isDark ? AppTheme.neutralGray100 : AppTheme.neutralGray900,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:dropdown_search/dropdown_search.dart';
import '../providers/app_providers.dart';
import '../../core/constants/ui_constants.dart';
import '../../app/theme/app_theme.dart';
import '../../data/models/app_model.dart';
import 'timeline_components.dart';
import 'common_ui_components.dart';

/// Optimized provider for dropdown items to prevent recreation
final dropdownItemsProvider = Provider<AsyncValue<List<AppModel>>>((ref) {
  final apps = ref.watch(appsProvider);
  return apps.when(
    data: (appList) {
      final allGamesOption = AppModel(id: -1, name: 'All Games');
      final dropdownItems = [allGamesOption, ...appList];
      return AsyncValue.data(dropdownItems);
    },
    loading: () => const AsyncValue.loading(),
    error: (error, stack) => AsyncValue.error(error, stack),
  );
});

class DialogComponents {
  static void showAddAppDialog(BuildContext context, WidgetRef ref) {
    final controller = TextEditingController();
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: isDark ? theme.cardBg : theme.colorScheme.surface,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(UIConstants.radiusXL),
        ),
        title: Text(
          'Add New Game',
          style: theme.textTheme.titleLarge?.copyWith(
            color: isDark ? AppTheme.neutralGray100 : AppTheme.neutralGray900,
            fontWeight: FontWeight.w700,
          ),
        ),
        content: TextField(
          controller: controller,
          decoration: InputDecoration(
            hintText: 'Enter game name...',
            hintStyle: TextStyle(
              color: isDark ? AppTheme.neutralGray400 : AppTheme.neutralGray600,
            ),
            labelText: 'Game Name',
            labelStyle: TextStyle(
              color: isDark ? AppTheme.primaryColor : theme.colorScheme.primary,
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(UIConstants.radiusM),
            ),
          ),
          style: TextStyle(
            color: isDark ? AppTheme.neutralGray100 : AppTheme.neutralGray900,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            style: TextButton.styleFrom(
              foregroundColor: isDark ? AppTheme.neutralGray400 : AppTheme.neutralGray600,
            ),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              if (controller.text.isNotEmpty) {
                ref.read(appsProvider.notifier).addApp(controller.text);
                Navigator.of(context).pop();
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: isDark ? AppTheme.primaryColor : theme.colorScheme.primary,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(UIConstants.radiusM),
              ),
            ),
            child: const Text('Add Game'),
          ),
        ],
      ),
    );
  }

  static void showFilterDialog(BuildContext context, WidgetRef ref) {
    final searchState = ref.read(appSearchProvider);
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: isDark ? theme.cardBg : theme.colorScheme.surface,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(UIConstants.radiusXL),
        ),
        title: Row(
          children: [
            Container(
              padding: EdgeInsets.all(UIConstants.spacingS.w),
              decoration: BoxDecoration(
                color: (isDark ? AppTheme.primaryColor : theme.colorScheme.primary).withOpacity(0.2),
                borderRadius: BorderRadius.circular(UIConstants.radiusM),
              ),
              child: Icon(
                Icons.filter_list,
                color: isDark ? AppTheme.primaryColor : theme.colorScheme.primary,
                size: UIConstants.iconM,
              ),
            ),
            SizedBox(width: UIConstants.spacingM.w),
            Text(
              'Sort & Filter Games',
              style: theme.textTheme.titleLarge?.copyWith(
                color: isDark ? AppTheme.neutralGray100 : AppTheme.neutralGray900,
                fontWeight: FontWeight.w700,
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Sort by:',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: isDark ? AppTheme.neutralGray200 : AppTheme.neutralGray800,
              ),
            ),
            SizedBox(height: UIConstants.spacingS.h),
            DropdownButtonFormField<AppSortBy>(
              value: searchState.sortBy,
              decoration: InputDecoration(
                labelText: 'Sort by',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(UIConstants.radiusM),
                ),
                filled: true,
                fillColor: isDark ? AppTheme.neutralGray800 : AppTheme.neutralGray100,
              ),
              dropdownColor: isDark ? AppTheme.neutralGray800 : Colors.white,
              style: TextStyle(
                color: isDark ? AppTheme.neutralGray100 : AppTheme.neutralGray900,
              ),
              items: AppSortBy.values.map((sortBy) {
                return DropdownMenuItem(
                  value: sortBy,
                  child: Text(_getSortByLabel(sortBy)),
                );
              }).toList(),
              onChanged: (value) {
                if (value != null) {
                  ref.read(appSearchProvider.notifier).updateSortBy(value);
                }
              },
            ),
            SizedBox(height: UIConstants.spacingM.h),
            Text(
              'Sort order:',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: isDark ? AppTheme.neutralGray200 : AppTheme.neutralGray800,
              ),
            ),
            SizedBox(height: UIConstants.spacingS.h),
            DropdownButtonFormField<SortOrder>(
              value: searchState.sortOrder,
              decoration: InputDecoration(
                labelText: 'Sort order',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(UIConstants.radiusM),
                ),
                filled: true,
                fillColor: isDark ? AppTheme.neutralGray800 : AppTheme.neutralGray100,
              ),
              dropdownColor: isDark ? AppTheme.neutralGray800 : Colors.white,
              style: TextStyle(
                color: isDark ? AppTheme.neutralGray100 : AppTheme.neutralGray900,
              ),
              items: SortOrder.values.map((order) {
                return DropdownMenuItem(
                  value: order,
                  child: Text(_getSortOrderLabel(order)),
                );
              }).toList(),
              onChanged: (value) {
                if (value != null) {
                  ref.read(appSearchProvider.notifier).updateSortOrder(value);
                }
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              ref.read(appSearchProvider.notifier).clearAllFilters();
              Navigator.of(context).pop();
            },
            style: TextButton.styleFrom(
              foregroundColor: isDark ? AppTheme.neutralGray400 : AppTheme.neutralGray600,
            ),
            child: const Text('Clear All'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            style: TextButton.styleFrom(
              foregroundColor: isDark ? AppTheme.primaryColor : theme.colorScheme.primary,
            ),
            child: const Text('Done'),
          ),
        ],
      ),
    );
  }

  static void showTimelineFilterDialog(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    showDialog(
      context: context,
      builder: (context) => Consumer(
        builder: (context, ref, child) {
          final filterState = ref.watch(timelineFilterProvider);

          return AlertDialog(
            backgroundColor: isDark ? theme.cardBg : theme.colorScheme.surface,
            surfaceTintColor: Colors.transparent,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(UIConstants.radiusXL),
            ),
            title: Row(
              children: [
                Container(
                  padding: EdgeInsets.all(UIConstants.spacingS.w),
                  decoration: BoxDecoration(
                    color: (isDark ? AppTheme.primaryColor : theme.colorScheme.primary).withOpacity(0.2),
                    borderRadius: BorderRadius.circular(UIConstants.radiusM),
                  ),
                  child: Icon(
                    Icons.filter_list,
                    color: isDark ? AppTheme.primaryColor : theme.colorScheme.primary,
                    size: UIConstants.iconM,
                  ),
                ),
                SizedBox(width: UIConstants.spacingM.w),
                Text(
                  'Timeline Filters',
                  style: theme.textTheme.titleLarge?.copyWith(
                    color: isDark ? Colors.white : theme.colorScheme.onSurface,
                    fontWeight: FontWeight.w700,
                  ),
                ),
              ],
            ),
            content: SizedBox(
              width: 400,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Game Filter Section
                  _GameFilterSection(filterState: filterState, isDark: isDark, theme: theme),

                  // Time Period Section
                  Text(
                    'Time Period:',
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: isDark ? AppTheme.neutralGray200 : AppTheme.neutralGray800,
                    ),
                  ),
                  SizedBox(height: UIConstants.spacingS.h),
                  DropdownButtonFormField<TimelineFilterType>(
                    value: filterState.filterType,
                    decoration: InputDecoration(
                      labelText: 'Time Period',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(UIConstants.radiusM),
                      ),
                      filled: true,
                      fillColor: isDark ? AppTheme.neutralGray800 : AppTheme.neutralGray100,
                    ),
                    dropdownColor: isDark ? AppTheme.neutralGray800 : Colors.white,
                    style: TextStyle(
                      color: isDark ? AppTheme.neutralGray100 : AppTheme.neutralGray900,
                    ),
                    items: TimelineFilterType.values.map((filterType) {
                      return DropdownMenuItem(
                        value: filterType,
                        child: Text(_getTimelineFilterTypeLabel(filterType)),
                      );
                    }).toList(),
                    onChanged: (value) {
                      if (value != null) {
                        ref.read(timelineFilterProvider.notifier).updateFilterType(value);
                      }
                    },
                  ),
                  SizedBox(height: UIConstants.spacingM.h),

                  // Sort By Section
                  Text(
                    'Sort by:',
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: isDark ? AppTheme.neutralGray200 : AppTheme.neutralGray800,
                    ),
                  ),
                  SizedBox(height: UIConstants.spacingS.h),
                  DropdownButtonFormField<TimelineSortBy>(
                    value: filterState.sortBy,
                    decoration: InputDecoration(
                      labelText: 'Sort by',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(UIConstants.radiusM),
                      ),
                      filled: true,
                      fillColor: isDark ? AppTheme.neutralGray800 : AppTheme.neutralGray100,
                    ),
                    dropdownColor: isDark ? AppTheme.neutralGray800 : Colors.white,
                    style: TextStyle(
                      color: isDark ? AppTheme.neutralGray100 : AppTheme.neutralGray900,
                    ),
                    items: TimelineSortBy.values.map((sortBy) {
                      return DropdownMenuItem(
                        value: sortBy,
                        child: Text(_getTimelineSortByLabel(sortBy)),
                      );
                    }).toList(),
                    onChanged: (value) {
                      if (value != null) {
                        ref.read(timelineFilterProvider.notifier).updateSortBy(value);
                      }
                    },
                  ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () {
                  ref.read(timelineFilterProvider.notifier).clearFilters();
                  Navigator.of(context).pop();
                },
                style: TextButton.styleFrom(
                  foregroundColor: isDark ? AppTheme.neutralGray400 : AppTheme.neutralGray600,
                ),
                child: const Text('Clear All'),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                style: TextButton.styleFrom(
                  foregroundColor: isDark ? AppTheme.primaryColor : theme.colorScheme.primary,
                ),
                child: const Text('Done'),
              ),
            ],
          );
        },
      ),
    );
  }

  static String _getSortByLabel(AppSortBy sortBy) {
    switch (sortBy) {
      case AppSortBy.name:
        return 'Name';
      case AppSortBy.duration:
        return 'Total Time';
      case AppSortBy.launches:
        return 'Times Played';
      case AppSortBy.lastUsed:
        return 'Last Played';
    }
  }

  static String _getSortOrderLabel(SortOrder order) {
    switch (order) {
      case SortOrder.ascending:
        return 'Ascending';
      case SortOrder.descending:
        return 'Descending';
    }
  }

  static String _getTimelineFilterTypeLabel(TimelineFilterType type) {
    switch (type) {
      case TimelineFilterType.all:
        return 'All Time';
      case TimelineFilterType.today:
        return 'Today';
      case TimelineFilterType.yesterday:
        return 'Yesterday';
      case TimelineFilterType.thisWeek:
        return 'This Week';
      case TimelineFilterType.lastWeek:
        return 'Last Week';
      case TimelineFilterType.thisMonth:
        return 'This Month';
      case TimelineFilterType.lastMonth:
        return 'Last Month';
      case TimelineFilterType.custom:
        return 'Custom Range';
    }
  }

  static String _getTimelineSortByLabel(TimelineSortBy sortBy) {
    switch (sortBy) {
      case TimelineSortBy.dateNewest:
        return 'Newest First';
      case TimelineSortBy.dateOldest:
        return 'Oldest First';
      case TimelineSortBy.durationLongest:
        return 'Longest Sessions';
      case TimelineSortBy.durationShortest:
        return 'Shortest Sessions';
      case TimelineSortBy.appName:
        return 'Game Name';
    }
  }
}

/// Optimized game filter section widget for better performance
class _GameFilterSection extends ConsumerWidget {
  final TimelineFilterState filterState;
  final bool isDark;
  final ThemeData theme;

  const _GameFilterSection({
    required this.filterState,
    required this.isDark,
    required this.theme,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final dropdownItems = ref.watch(dropdownItemsProvider);

    return dropdownItems.when(
      data: (items) {
        // Use efficient lookup for selected item
        AppModel? selectedItem;
        if (filterState.selectedAppId == null) {
          selectedItem = items.first; // First item is "All Games"
        } else {
          selectedItem = items.cast<AppModel?>().firstWhere(
            (app) => app?.id == filterState.selectedAppId,
            orElse: () => items.first,
          );
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Game Filter:',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: isDark ? AppTheme.neutralGray200 : AppTheme.neutralGray800,
              ),
            ),
            SizedBox(height: UIConstants.spacingS.h),
            DropdownSearch<AppModel?>(
              items: (filter, infiniteScrollProps) => items,
              selectedItem: selectedItem,
              compareFn: (item1, item2) => item1?.id == item2?.id,
              filterFn: (item, filter) {
                if (filter.isEmpty) return true;
                final itemName = item?.name?.toLowerCase() ?? 'all games';
                final filterLower = filter.toLowerCase();
                return itemName.contains(filterLower);
              },
              dropdownBuilder: (context, selectedItem) {
                return Text(
                  selectedItem?.name ?? 'All Games',
                  style: TextStyle(
                    color: isDark ? AppTheme.neutralGray100 : AppTheme.neutralGray900,
                    fontSize: 16,
                  ),
                );
              },
              decoratorProps: DropDownDecoratorProps(
                decoration: InputDecoration(
                  hintText: 'Search games...',
                  hintStyle: TextStyle(
                    color: isDark ? AppTheme.neutralGray400 : AppTheme.neutralGray600,
                  ),
                  filled: true,
                  fillColor: isDark ? AppTheme.neutralGray800 : AppTheme.neutralGray100,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(UIConstants.radiusM),
                    borderSide: BorderSide(
                      color: isDark ? AppTheme.neutralGray700 : AppTheme.neutralGray300,
                    ),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(UIConstants.radiusM),
                    borderSide: BorderSide(
                      color: isDark ? AppTheme.neutralGray700 : AppTheme.neutralGray300,
                    ),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(UIConstants.radiusM),
                    borderSide: BorderSide(
                      color: isDark ? AppTheme.primaryColor : theme.colorScheme.primary,
                      width: 2,
                    ),
                  ),
                  contentPadding: EdgeInsets.symmetric(
                    horizontal: UIConstants.spacingM.w,
                    vertical: UIConstants.spacingM.h,
                  ),
                ),
              ),
              popupProps: PopupProps.menu(
                showSearchBox: true,
                searchDelay: const Duration(milliseconds: 200),
                searchFieldProps: TextFieldProps(
                  decoration: InputDecoration(
                    hintText: 'Search games...',
                    hintStyle: TextStyle(
                      color: isDark ? AppTheme.neutralGray400 : AppTheme.neutralGray600,
                    ),
                    prefixIcon: Icon(
                      Icons.search,
                      color: isDark ? AppTheme.neutralGray400 : AppTheme.neutralGray600,
                    ),
                    filled: true,
                    fillColor: isDark ? AppTheme.neutralGray800 : AppTheme.neutralGray100,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(UIConstants.radiusM),
                      borderSide: BorderSide.none,
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(UIConstants.radiusM),
                      borderSide: BorderSide.none,
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(UIConstants.radiusM),
                      borderSide: BorderSide(
                        color: isDark ? AppTheme.primaryColor : theme.colorScheme.primary,
                        width: 2,
                      ),
                    ),
                    contentPadding: EdgeInsets.symmetric(
                      horizontal: UIConstants.spacingM.w,
                      vertical: UIConstants.spacingS.h,
                    ),
                  ),
                  style: TextStyle(
                    color: isDark ? AppTheme.neutralGray100 : AppTheme.neutralGray900,
                  ),
                ),
                menuProps: MenuProps(
                  backgroundColor: isDark ? AppTheme.neutralGray800 : Colors.white,
                  elevation: 8,
                  borderRadius: BorderRadius.circular(UIConstants.radiusM),
                  clipBehavior: Clip.antiAlias,
                ),
                itemBuilder: (context, item, isDisabled, isSelected) {
                  final isAllGames = item?.id == -1;
                  final itemName = item?.name ?? 'All Games';

                  return Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: UIConstants.spacingM.w,
                      vertical: UIConstants.spacingS.h,
                    ),
                    color: isSelected
                        ? (isDark ? AppTheme.primaryColor.withOpacity(0.15) : theme.colorScheme.primary.withOpacity(0.08))
                        : null,
                    child: Row(
                      children: [
                        Icon(
                          isAllGames ? Icons.apps : Icons.games,
                          color: isAllGames
                              ? (isDark ? AppTheme.neutralGray400 : AppTheme.neutralGray600)
                              : AppTheme.infoColor,
                          size: 18,
                        ),
                        SizedBox(width: UIConstants.spacingM.w),
                        Expanded(
                          child: Text(
                            itemName,
                            style: TextStyle(
                              color: isDark ? AppTheme.neutralGray100 : AppTheme.neutralGray900,
                              fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                              fontSize: 14,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        if (isSelected)
                          Icon(
                            Icons.check,
                            color: isDark ? AppTheme.primaryColor : theme.colorScheme.primary,
                            size: 16,
                          ),
                      ],
                    ),
                  );
                },
                fit: FlexFit.loose,
                constraints: const BoxConstraints(maxHeight: 300),
              ),
              onChanged: (selectedApp) {
                // Fix the game filter deletion issue
                final appId = selectedApp?.id == -1 ? null : selectedApp?.id;
                ref.read(timelineFilterProvider.notifier).updateSelectedApp(appId);
              },
            ),
            SizedBox(height: UIConstants.spacingL.h),
          ],
        );
      },
      loading: () => const SizedBox.shrink(),
      error: (_, __) => const SizedBox.shrink(),
    );
  }
}